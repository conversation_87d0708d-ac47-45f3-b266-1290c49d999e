FROM node:20-alpine AS development
ARG NPM_WORKSPACE=nemo-core-service
ENV NODE_ENV=development
WORKDIR /usr/src/app
COPY ./package*.json ./
COPY ./.npmrc ./
COPY ./packages ./packages
COPY ./apps/${NPM_WORKSPACE}/src ./apps/${NPM_WORKSPACE}/src
COPY ./apps/${NPM_WORKSPACE}/package.json ./apps/${NPM_WORKSPACE}/package.json
COPY ./apps/${NPM_WORKSPACE}/tsconfig.json ./apps/${NPM_WORKSPACE}/tsconfig.json
COPY ./apps/${NPM_WORKSPACE}/nest-cli.json ./apps/${NPM_WORKSPACE}/nest-cli.json
COPY ./apps/${NPM_WORKSPACE}/scripts/ ./apps/${NPM_WORKSPACE}/scripts/
RUN npm ci

# # Builder
FROM node:20-alpine AS builder
ARG NPM_WORKSPACE=nemo-core-service
WORKDIR /usr/src/app
COPY --from=development /usr/src/app/ ./
# Must not do here
# RUN npm run build -w packages/contracts
RUN npm run build -w ${NPM_WORKSPACE}
RUN npm prune -w ${NPM_WORKSPACE} --omit=dev

# # Production
FROM node:20-alpine AS production
ARG NPM_WORKSPACE=nemo-core-service
ENV NODE_ENV=production
WORKDIR /usr/src/app
COPY --from=builder /usr/src/app/packages ./packages
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/apps/${NPM_WORKSPACE}/package.json ./package.json
COPY --from=builder /usr/src/app/apps/${NPM_WORKSPACE}/node_modules ./dist/node_modules
COPY --from=builder /usr/src/app/apps/${NPM_WORKSPACE}/dist/ ./dist/src/
COPY --from=builder /usr/src/app/apps/${NPM_WORKSPACE}/scripts/ ./scripts/
RUN chown -R node:node /usr/src/app
USER node
CMD [ "node", "dist/src/apps/nemo-core-service/src/main.js" ]
