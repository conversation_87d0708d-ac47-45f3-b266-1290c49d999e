export enum BranchTypeEnum {
  SHOP = 'Shop',
  WAREHOUSE = 'Warehouse',
  DEALER = 'Dealer',
  PARTNER = 'Partner',
  ADMIN = 'Admin',
}

export const AO_FROM_BRANCH_TYPES = [BranchTypeEnum.SHOP, BranchTypeEnum.WAREHOUSE]
export const AO_TO_BRANCH_TYPES = [BranchTypeEnum.SHOP, BranchTypeEnum.WAREHOUSE, BranchTypeEnum.DEALER]

export type TargetBranchType = 'FROM_BRANCH' | 'TO_BRANCH'
export const acceptableAoBranchTypesByTargetBranch: Record<TargetBranchType, BranchTypeEnum[]> = {
  FROM_BRANCH: AO_FROM_BRANCH_TYPES,
  TO_BRANCH: AO_TO_BRANCH_TYPES,
}
