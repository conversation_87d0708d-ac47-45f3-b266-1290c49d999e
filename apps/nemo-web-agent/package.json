{"name": "nemo-web-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint --fix .", "export": "next build && next export", "test": "jest", "test:coverage": "jest --coverage --detectOpenHandles --config=jest.config.js __tests__/*"}, "dependencies": {"@material-tailwind/react": "^2.1.4", "@next/third-parties": "^15.1.1", "cerialize": "^0.1.18", "html5-qrcode": "^2.3.8", "luxon": "^3.4.4", "next": "^14.2.25", "react": "^18", "react-dom": "^18", "react-webcam": "^7.2.0", "ui": "*", "utils": "*", "nemo-common": "*", "zustand": "^4.4.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "config": "*", "contracts": "*", "eslint": "*", "eslint-config-custom": "*", "eslint-config-next": "13.5.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lodash": "*", "next-router-mock": "^0.9.10", "typescript": "^5"}}