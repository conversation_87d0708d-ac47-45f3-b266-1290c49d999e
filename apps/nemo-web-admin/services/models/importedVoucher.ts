import { autoserializeAs } from 'cerialize'

import { GetCountVocuherResponse } from 'contracts'

export class ImportedVoucher implements GetCountVocuherResponse {
  @autoserializeAs('providerName')
  providerName!: string
  @autoserializeAs('voucherValue')
  voucherValue!: number
  @autoserializeAs('total')
  total!: number
  @autoserializeAs('available')
  available!: number
  @autoserializeAs('used')
  used!: number
}
