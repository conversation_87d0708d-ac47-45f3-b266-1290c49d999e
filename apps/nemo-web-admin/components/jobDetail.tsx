import { Fragment, useEffect, useMemo, useState, useCallback } from 'react'
import { useRouter } from 'next/router'
import { useShallow } from 'zustand/react/shallow'

import { useTranslation } from '@/hooks'
import 'ui/src/survey/ActionItems'
import { LayoutPage, modifyCrumbs } from '@/components/layout'
import { SurveyViewDetail, Chip, Breadcrumb, PageTitle, Button, IQuestionAnswer } from 'ui'
import { useJobsStore, IDataChangeKeep, IJobModal } from '@/stores/jobsStore'
import { Comments, Conversation } from '@/services/models/comments'
import { Job, JobStatus } from '@/services/models/jobs'
import {
  displayIsDate,
  getColorChipFromCodeStatus,
  getStatusFromCodeStatus,
  defaultValue,
  scrollToFirstError,
} from '@/utils'
import { Model, GetQuestionTitleActionsEvent, Serializer } from 'survey-core'
//Add a custom 'tooltip' property to questions of all types and to pagesfMyJobsDetailPage
Serializer.addProperty('question', 'tooltip:obj')
import { theme } from '@/survey-forms'
import { Survey } from 'survey-react-ui'
import { collection, onSnapshot, doc, getDocs, query, where, orderBy, getFirestore } from 'firebase/firestore'
import { useUserStore } from '@/stores/userStore'
import { useSystemStore } from '@/stores/systemStore'
import { usePersistStore } from '@/stores/persistStore'
import { useCompanyStore } from '@/stores/companyStore'
import { CommonModalContext, AdminMenuSlug } from 'ui/src/models'
import { crumbs, urlToTextMap } from '@/config/breadcrumb'
import 'survey-core/defaultV2.min.css'
import CommentDrawer from '@/components/CommentDrawer'
import Link from 'next/link'

import {
  OverwriteSurveyRemobieCheckList,
  IRemobieChecklist,
  defaultRemobieChecklist,
  REMOBIE_SLUG_NAME,
} from '@/components/overwriteSurvey/remobieCheckList'
import { PriceFooter } from '@/components/priceFooter'
import { PriceFooterKF } from '@/components/priceFooterKF'
import {
  defaultQuestionChecklist,
  IQuestionChecklist,
  OverwriteQuestion,
  PRODUCT_INFORMATION,
} from './overwriteSurvey/overwriteQuestion'
import { PermissionAction, PermissionMapCode } from 'utils'
import { VendorTypeEnum } from 'nemo-common'

interface DisplayStaticData {
  title: string
  displayValue: any
  isDate?: boolean
}

enum OptionChoiceMapPenalty {
  SELECT = 'select',
  NOT_SELECT = 'not_select',
  SKIP = 'skip',
}

enum CustomSection {
  REMOBIE_CHECK_LIST = 'remobie_check_list',
  PRODUCT_INFORMATION = 'product_information',
}
// --- CONSTANT --- //
const ADDITIONAL_PROD_INFO = 'additional_product_information'
const PRODUCT_IMAGES = 'product_images'
const PRODUCT_ADDITIONAL_IMAGES = 'product_additional_images'
// --- FUNCTION --- //
const generateEncodedKeys = (formData: any, parentPath: string[] = []): Set<string> => {
  const out = new Set<string>()

  if (!formData) return out

  for (const k of Object.keys(formData)) {
    const key = k as keyof typeof formData
    const val = formData[key]
    const path: string[] = [...parentPath, key as string]

    if (typeof val === 'object') {
      const r = generateEncodedKeys(val, path)
      for (const k of Array.from(r)) {
        out.add(k)
      }
    } else {
      out.add(`${path.join('.')}=${val}`)
    }
  }
  return out
}

// --- MAIN COMPONENT --- //
export const JobDetail = ({
  jobId,
  pageType,
  pageTitle = '',
  fromId,
  isOwner,
}: {
  jobId: string
  pageType: string
  pageTitle?: string
  fromId?: string
  isOwner?: boolean
}) => {
  const { t } = useTranslation('common')
  const router = useRouter()

  // --- USE STORE HERE --- //
  const { user } = useUserStore()
  const {
    job,
    jobsHx,
    jobAtMount,
    realtimeJobModal,
    fetchJobs,
    fetchJobById,
    fetchJobCount,
    clearJob,
    assignJob,
    suggestPrice,
    storeDataChangeKeep,
    clearDataChangeKeep,
    clearRealtimeJobModal,
  } = useJobsStore(
    useShallow(state => {
      return {
        job: state.job,
        jobsHx: state.jobsHx,
        jobAtMount: state.jobAtMount,
        realtimeJobModal: state.realtimeJobModal,
        fetchJobs: state.fetchJobs,
        fetchJobById: state.fetchJobById,
        fetchJobCount: state.fetchJobCount,
        clearJob: state.clearJob,
        suggestPrice: state.suggestPrice,
        assignJob: state.assignJob,
        storeDataChangeKeep: state.storeDataChangeKeep,
        clearDataChangeKeep: state.clearDataChangeKeep,
        clearRealtimeJobModal: state.clearRealtimeJobModal,
      }
    })
  )
  const { showCommonModal, hideCommonModal, setPopupClueModalConfig, permissionConfig } = useSystemStore()
  const { getCompanyId } = useCompanyStore(
    useShallow(state => {
      return {
        getCompanyId: state.getCompanyId,
      }
    })
  )

  // --- USE STATE HERE --- //
  const [additionalSurvey, setAdditionalSurvey] = useState<Model | null>(new Model())
  const [additionalSurveyTitleName, setAdditionalSurveyTitleName] = useState<string>()
  const [additionalCondition, setAdditionalCondition] = useState<{ [key: string]: string }>() // result of additional survey (object)
  const [calculatedPrice, setCalculatedPrice] = useState<number>(0)
  const [selectedPrice, setSelectedPrice] = useState<{ price: number; isManual: boolean }>({
    price: 0,
    isManual: false,
  })
  const [callKeepDataChange, setCallKeepDataChange] = useState<boolean>(false)
  const [historyId, setHistoryId] = useState<string | null>(null)
  const [remobieChecklist, setRemobieChecklist] = useState<IRemobieChecklist>(defaultRemobieChecklist)
  const [questionChecklist, setQuestionChecklist] = useState<IQuestionChecklist>(defaultQuestionChecklist)
  const [productInfoChecklistValue, setProductInfoChecklistValue] =
    useState<IQuestionChecklist>(defaultQuestionChecklist)
  const [isOpenComment, setIsOpenComment] = useState<boolean>(false)
  const [onNotiFromDifferenceID, setOnNotiFromDifferenceID] = useState<boolean>(false)
  const [commentConversation, setCommentConversation] = useState<Comments[] | undefined>()
  const [inhibitLeaveModalSpecialCondition, setInhibitLeaveModalSpecialCondition] = useState<boolean>(false)
  const [isRefresh, setIsRefresh] = useState<boolean>(false)
  const [questionValidateFailAfterSubmit, setQuestionValidateFailAfterSubmit] = useState<boolean>(false)

  const { lastOpenedCommentInMilliseconds, setLastOpenedCommentInMilliseconds } = usePersistStore()

  // --- CHAT RELATE --- //
  const hasUnreadComment: boolean = useMemo(() => {
    if (!commentConversation) {
      return false
    }
    const lastTimeOpen = lastOpenedCommentInMilliseconds[jobId] || 0
    const lastComment = commentConversation?.[commentConversation.length - 1] || ({} as any)
    const { createdBy, createdAt } = lastComment
    if (createdBy === undefined || user?.userKey === createdBy || lastTimeOpen > createdAt) {
      return false
    }
    return true
  }, [commentConversation, lastOpenedCommentInMilliseconds])

  const fetchComment = async (jobId: string) => {
    const parentRef = doc(getFirestore(), `company/${getCompanyId()}/conversation/${jobId}`)
    const nestedRef = collection(parentRef, 'activity')
    const queryComment = query(nestedRef, where('type', '==', 'comment'), orderBy('createAt'))

    const querySnapshot = await getDocs(queryComment)

    const comments: Comments[] = []

    querySnapshot.forEach(data => {
      const conversations = Conversation.toInstance(data.data())
      const comment: Comments = conversations.getConversationComments

      comments.push(comment)
    })

    setCommentConversation(comments)
  }

  useEffect(() => {
    if (job?.jobId) fetchComment(jobId)
  }, [job])

  const openConfirmSuggestPriceModal = (price: number, grade: string, job: Job) => {
    if (!isProductInfoAnswerValid()) {
      setQuestionValidateFailAfterSubmit(true)
      return
    }

    showCommonModal({
      title: t('jobs-detail.estimate-comfirm-title'),
      subTitle: t('baht', {
        price: price.toLocaleString(undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        }),
      }),
      description: t('jobs-detail.estimate-comfirm-description'),
      icon: 'ic-warning-estimated',
      onClose: () => hideCommonModal(),
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('confirm'),
      negativeButtonTxt: t('cancel'),
      onClickPositiveButton: () => {
        onClickToSuggestPrice(job, price, grade)
      },
      onClickNegativeButton: () => hideCommonModal(),
    })
  }

  const onClickToSuggestPrice = async (job: Job, price: number, grade: string) => {
    let body: any = { suggestedPrice: price, grade }
    let prepareAdminCheckListValues: any = {}

    // check change from admin
    if (remobieChecklist.adminChange) {
      prepareAdminCheckListValues = {
        ...prepareAdminCheckListValues,
        remobie_check_list: remobieChecklist.remobie_check_list,
      }
    }

    // check production information change from admin
    if (questionChecklist.adminChange) {
      // change format to save
      const adminChecklistValue: { [key: string]: string | string[] } = {}
      for (const key in productInfoChecklistValue[PRODUCT_INFORMATION]) {
        if (productInfoChecklistValue[PRODUCT_INFORMATION].hasOwnProperty(key)) {
          adminChecklistValue[key] = productInfoChecklistValue[PRODUCT_INFORMATION][key].answer
        }
      }

      prepareAdminCheckListValues = {
        ...prepareAdminCheckListValues,
        product_information: adminChecklistValue,
      }
    }

    // assign value to body
    if (Object.keys(prepareAdminCheckListValues).length > 0) {
      body = { ...body, adminCheckListValues: prepareAdminCheckListValues }
    }

    if (job.status === JobStatus.ESTIMATE_PRICE_PROCESSING) {
      const success = await suggestPrice(job.jobId, body, error => {
        if (error.body?.code === '101003') {
          modalConfig('REJECT_BY_SHOP')
        }
        if (error.body?.code === '103005') {
          modalConfig('VOUCHER_NOT_FOUND')
        }
      })

      if (success) {
        fetchJobCount()
        hideCommonModal()
        if (pageType === 'all-jobs') {
          router.push(`/${AdminMenuSlug.allJob}/success?id=${job.jobId}`)
        } else {
          router.push(`/${AdminMenuSlug.myJob}/success?id=${job.jobId}`)
        }
      }
    }
  }

  const onSurveyValueChanged = (sender: Model, _: any) => {
    setAdditionalCondition(sender.getAllValues())
  }

  const addTooltip = (_: Model, options: GetQuestionTitleActionsEvent) => {
    if (options.question.tooltip) {
      options.titleActions = [
        {
          title: 'i',
          tooltip: JSON.stringify({
            title: options.question.tooltip.title,
            desc: options.question.tooltip.desc,
            html: options.question.tooltip.html,
          }),
          action: () => {},
        },
      ]
    }
  }

  //=========== Confirmation leave modal section ===========//
  const onRouteChangeStart = useCallback(
    (nextPath: string) => {
      const pathList = nextPath.split('/')
      const paramsList = nextPath.split('?')[1]?.split('&')
      const params: any = {}
      paramsList?.forEach((p: string) => {
        const [key, value] = p.split('=')
        params[key] = value
      })

      const path = pathList[pathList.length - 1].split('?')[0]
      const idJob = fromId ?? jobId

      if (
        (fromId && isOwner && path !== 'login' && !(path === 'detail' && idJob === params.id)) ||
        (!fromId &&
          inhibitLeaveModalSpecialCondition !== true &&
          jobAtMount?.status === JobStatus.ESTIMATE_PRICE_PROCESSING &&
          user?.userKey === jobAtMount?.adminUserKey &&
          path !== 'login' &&
          !(path === 'success' && idJob === params.id) &&
          !(path === 'history' && idJob === params.fromId) &&
          !(path === 'detail' && idJob === params.id))
      ) {
        confirmLeaveModal(() => onConfirmRouteChange(nextPath))
        throw 'cancelRouteChange'
      }
    },
    [jobAtMount, user, jobId, fromId, isOwner, inhibitLeaveModalSpecialCondition]
  )

  const onConfirmRouteChange = (path: string) => {
    if (fromId && isOwner) {
      clearDataChangeKeep()
    }
    hideCommonModal()
    removeListener()
    router.push(path)
  }

  const removeListener = () => {
    router.events.off('routeChangeStart', onRouteChangeStart)
  }

  useEffect(() => {
    router.events.on('routeChangeStart', onRouteChangeStart)

    return removeListener
  }, [onRouteChangeStart])

  const confirmLeaveModal = (positiveFunction: () => void) => {
    showCommonModal({
      type: 'warning',
      title: t('jobs-detail.leave-page-comfirmation-title'),
      description: t('jobs-detail.leave-page-comfirmation-description'),
      onClose: hideCommonModal,
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('accept'),
      negativeButtonTxt: t('back'),
      onClickPositiveButton: positiveFunction,
      onClickNegativeButton: hideCommonModal,
    })
  }
  //=========== End confirmation leave section ===========//

  // --- USEEFFECT ---//
  useEffect(() => {
    if (
      router.pathname.split('/')[2] !== 'history' &&
      job &&
      user?.userKey !== job?.adminUserKey &&
      pageType === 'my-jobs'
    ) {
      router.replace(`/${AdminMenuSlug.allJob}/detail?id=${jobId}`)
    }
  }, [job])

  useEffect(() => {
    const refresh = () => setIsRefresh(true)
    window.addEventListener('beforeunload', refresh)
    return () => {
      window.removeEventListener('beforeunload', refresh)
    }
  }, [])

  useEffect(() => {
    if (isRefresh && isOpenComment) {
      setLastOpenedCommentInMilliseconds(jobId, new Date().getTime())
    }
  }, [isRefresh])

  useEffect(() => {
    if (jobId) {
      const subscribeDetailState = subscribeConversation(jobId.toString())

      return () => {
        subscribeDetailState?.()
        clearJob()
      }
    }
  }, [jobId])

  useEffect(() => {
    if (callKeepDataChange) {
      keepDataChange()
      setCallKeepDataChange(false)
    }
  }, [callKeepDataChange])

  useEffect(() => {
    if (jobAtMount) {
      findHx(jobAtMount.deviceKey)
      setCalculatedPrice(jobAtMount.suggestedPrice ?? 0)
      const template = jobAtMount.checkList.find(i => i.slug === ADDITIONAL_PROD_INFO)
      if (template) {
        const surveyTemplate = template.survey_form
        const titleName = template.survey_form.title ?? t('jobs-management.additional-product-title')
        setAdditionalSurveyTitleName(titleName)

        if (jobAtMount?.checkList?.length) {
          jobAtMount.checkList.forEach(template => {
            const slug = template.slug
            const answer = jobAtMount?.checkListValues?.[slug]

            if (slug === ADDITIONAL_PROD_INFO) {
              const additionalProdInfo = jobAtMount.adminCheckListValues?.additional_product_information || answer

              // disabled dropdown if status pending
              if (jobAtMount.status === JobStatus.QUOTE_REQUESTED) {
                surveyTemplate.pages[0].elements[0].enableIf = '1=0'
              }

              // set selected dropdown choices
              surveyTemplate.pages[0].elements[0].defaultValue = additionalProdInfo?.device_condition
                ? additionalProdInfo.device_condition
                : ''

              // book empty field
              surveyTemplate.pages[0].elements.push({
                type: 'text',
                name: 'emptyField',
                title: 'emptyField',
                startWithNewLine: false,
              })
              surveyTemplate.pages[0].elements.push({
                type: 'text',
                name: 'emptyField',
                title: 'emptyField',
                startWithNewLine: false,
              })
              setAdditionalCondition(additionalProdInfo)
            }
          })
        }

        const surveyModel = new Model(surveyTemplate)
        const completeButton = surveyModel.navigationBar.actions.find(x => x.id === 'sv-nav-complete')

        // set size for template
        surveyModel.setVariable('deviceSize', 'desktop')
        surveyModel.applyTheme(theme)

        // add tooltips id has tooltips node
        surveyModel.onGetQuestionTitleActions.add(addTooltip)

        // book empty field if not 3 fields
        surveyModel.onAfterRenderQuestion.add(function (sender, options) {
          if (options.question.name === 'emptyField') {
            options.htmlElement.style.opacity = '0'
            options.htmlElement.style.height = '0px'
          }
        })
        // surveyModel.defaultValue = 'no_marks_new'
        surveyModel.navigationBar.actions.splice(surveyModel.navigationBar.actions.indexOf(completeButton!), 1)
        surveyModel.hideRequiredErrors = true

        surveyModel.onComplete.add((_, options) => {
          console.log('onComplete')
        })

        setAdditionalSurvey(surveyModel)
      }

      if (jobAtMount.suggestedPrice === 0) {
        const productInformation = jobAtMount.checkListValues[PRODUCT_INFORMATION]
        const remobieCheckList = jobAtMount.checkListValues[REMOBIE_SLUG_NAME]
        setCalculatedPrice(
          calPrice(
            {
              [REMOBIE_SLUG_NAME]: remobieCheckList,
              [PRODUCT_INFORMATION]: productInformation,
            },
            jobAtMount.penalties
          )
        )
      }
    }
  }, [jobAtMount])

  useEffect(() => {
    if (job && job.suggestedPrice === 0) {
      const remobieCheckList = remobieChecklist[REMOBIE_SLUG_NAME]
      const productInformation = job.checkListValues[PRODUCT_INFORMATION]
      setCalculatedPrice(
        calPrice(
          {
            [REMOBIE_SLUG_NAME]: remobieCheckList,
            [PRODUCT_INFORMATION]: productInformation,
            [ADDITIONAL_PROD_INFO]: additionalCondition ?? {},
          },
          job.penalties
        )
      )
    }
  }, [remobieChecklist, additionalCondition])

  useEffect(() => {
    if (router.query.comment) {
      const idQuery = router.query.id
      const idJob = job?.jobId // Dev Baobao Note: ห้ามเปลี่ยนเป็น jobId เพราะ jobId อิงมาจาก router แต่ comment fetch เมื่อ job เปลี่ยนไม่ได้อิงจาก router comment เดิมที่ค้างอยู่จึงต้องใช้ตัวนี้ดักจับเท่านั้น
      if (idJob !== idQuery) {
        setOnNotiFromDifferenceID(true)
      } else {
        setIsOpenComment(true)
      }
    } else {
      setIsOpenComment(false)
    }
  }, [router.query.comment])

  useEffect(() => {
    if (commentConversation !== undefined && onNotiFromDifferenceID) {
      setIsOpenComment(true)
      setOnNotiFromDifferenceID(false)
    }
  }, [commentConversation])

  useEffect(() => {
    if (jobsHx.length) {
      const hxId = jobsHx[0].jobId
      setHistoryId(hxId)
    } else {
      setHistoryId(null)
    }
  }, [jobsHx])

  useEffect(() => {
    if (realtimeJobModal !== null) {
      modalConfig(realtimeJobModal)
      clearRealtimeJobModal()
    }
  }, [realtimeJobModal])

  useEffect(() => {
    if (jobAtMount && jobAtMount.suggestedPrice === 0) {
      const productInfoData = questionChecklist[PRODUCT_INFORMATION]
      const remobieCheckList = jobAtMount.checkListValues[REMOBIE_SLUG_NAME]

      // find key answer
      const hasAnswerkey = findAnswerkey(productInfoData)

      // convert array type to string value select or not_select
      const adjustedAnswer: any = {}
      if (!hasAnswerkey) {
        for (const key in productInfoData) {
          if (Array.isArray(productInfoData[key])) {
            adjustedAnswer[key] = productInfoData[key].length > 0 ? 'select' : 'not_select'
          } else {
            adjustedAnswer[key] = productInfoData[key]
          }
        }
      }

      const productInformation = hasAnswerkey ? answerToCal(productInfoData) : adjustedAnswer
      setCalculatedPrice(
        calPrice(
          {
            [REMOBIE_SLUG_NAME]: remobieCheckList,
            [PRODUCT_INFORMATION]: productInformation,
          },
          jobAtMount.penalties
        )
      )

      setProductInfoChecklistValue(questionChecklist)
    }
  }, [questionChecklist])

  const findAnswerkey = (data: any) => {
    for (const key in data) {
      if (data[key].hasOwnProperty('answer')) {
        return true
      }
    }

    return false
  }
  const answerToCal = (questionAnswer: IQuestionAnswer | undefined, isNeedAnswerArray?: boolean) => {
    const adjustedAnswer: any = {}
    if (!questionAnswer) {
      return {}
    }
    Object.keys(questionAnswer).forEach(key => {
      if (!questionAnswer[key].answer) {
        adjustedAnswer[key] = OptionChoiceMapPenalty.SKIP
      } else {
        if (typeof questionAnswer[key].answer === 'string' || isNeedAnswerArray) {
          adjustedAnswer[key] = questionAnswer[key].isValid ? questionAnswer[key].answer : OptionChoiceMapPenalty.SKIP
        } else {
          if (questionAnswer[key].answer.length > 0) {
            adjustedAnswer[key] = OptionChoiceMapPenalty.SELECT
          } else {
            if (questionAnswer[key].isValid) {
              adjustedAnswer[key] = OptionChoiceMapPenalty.NOT_SELECT
            } else {
              adjustedAnswer[key] = OptionChoiceMapPenalty.SKIP
            }
          }
        }
      }
    })

    return adjustedAnswer
  }

  const subscribeConversation = (jobId: string) => {
    let isFirstSubscribe = true

    const queryPath = `company/${getCompanyId()}/conversation/${jobId}/activity`
    const conversationColRef = collection(getFirestore(), queryPath)

    return onSnapshot(conversationColRef, () => {
      if (!isFirstSubscribe) {
        fetchJobById({ id: jobId, isFetchSeamless: true })
      } else {
        fetchJobById({ id: jobId, isFetchSeamless: false, needSetJobAtMount: true })
      }
      isFirstSubscribe = false
    })
  }

  const calPrice = (checkListValues: object, penalties: { [key: string]: string }) => {
    if (!checkListValues) {
      return 0
    }
    const flatChecklist = generateEncodedKeys(checkListValues)
    let price = job?.getMaxPrice() || 1
    for (const key of Array.from(flatChecklist)) {
      price += Number(defaultValue(penalties?.[key], '0'))
    }
    return price >= 0 ? price : 0
  }

  const isProductInfoAnswerValid = (): boolean => {
    const productInfo = questionChecklist[PRODUCT_INFORMATION]
    let isValid = true
    if (!findAnswerkey(productInfo)) return true

    Object.keys(productInfo).forEach(key => {
      if (!productInfo[key]['isValid']) {
        scrollToFirstError(`[class*="${key}"]`)
        isValid = false
      }
    })

    return isValid
  }
  // --- [FN] STORE RELATE ---//
  const keepDataChange = () => {
    const data: IDataChangeKeep = { id: jobId, isOwner: user?.userKey === jobAtMount?.adminUserKey }
    if (remobieChecklist.adminChange) {
      data.remobieCheckList = remobieChecklist[REMOBIE_SLUG_NAME]
    }
    if (selectedPrice.isManual) {
      data.suggestedPrice = selectedPrice.price
    }

    let isValid = true
    Object.keys(questionChecklist[PRODUCT_INFORMATION]).forEach(key => {
      if (questionChecklist[PRODUCT_INFORMATION][key]['isValid'] === false) {
        isValid = false
      }
    })

    if (!isValid || questionChecklist.adminChange) {
      data.productInformation = answerToCal(questionChecklist[PRODUCT_INFORMATION], true)
    }

    storeDataChangeKeep(data)
  }
  // --- [FN] TO HX SECTION ---//
  const findHx = useCallback(async (deviceKey: string) => {
    await fetchJobs({ deviceKey, status: 'success' }, true)
  }, [])

  const navigateToHx = useCallback(() => {
    setCallKeepDataChange(true)
    router.push({
      pathname: `/${pageType}/history`,
      query: { id: historyId, fromId: jobId },
    })
  }, [jobId, historyId, user, jobAtMount])
  // --- [FN] MODAL ASSIGN AND REALTIME ---//
  const onClickAssignToMe = useCallback(async () => {
    const assignResult = await assignJob(jobId, errorStatus => {
      const errorCode = errorStatus.body?.code
      if (errorCode === '101003') {
        modalConfig('REJECT_BY_SHOP')
      } else {
        modalConfig('ASSIGN_OTHER_ADMIN')
      }
    })
    if (assignResult) {
      modalConfig('SUCCESS_ASSIGN')
    }
  }, [jobId])

  const modalConfig = useCallback((type: IJobModal) => {
    const modalProps: CommonModalContext = {
      onClose: () => onCloseModalAssign({}),
      onClickPositiveButton: () => onCloseModalAssign({}),
      positiveButtonTxt: t('agree'),
    }

    if (type === 'SUCCESS_ASSIGN') {
      modalProps.type = 'success'
      modalProps.buttonType = 'vertical-duo'
      modalProps.title = t('assign-job.success-title')
      modalProps.description = t('assign-job.success-desc')
      modalProps.positiveButtonTxt = t('assign-job.make-an-estimate')
      modalProps.negativeButtonTxt = t('assign-job.accept-other-jobs')
      modalProps.onClickNegativeButton = () =>
        onCloseModalAssign({ path: `/${AdminMenuSlug.allJob}`, query: { status: 'pending' } })
    } else {
      modalProps.type = 'warning'
      modalProps.buttonType = 'single'
      if (type === 'REJECT_BY_SHOP') {
        modalProps.title = t('assign-job.fail-cancel-title')
        modalProps.description = t('assign-job.fail-cancel-desc')
        modalProps.icon = 'ic-warning-canceled-estimated'
      } else if (type === 'ASSIGN_OTHER_ADMIN') {
        modalProps.title = t('assign-job.fail-title')
        modalProps.description = t('assign-job.fail-desc')
      } else if (type === 'VOUCHER_NOT_FOUND') {
        modalProps.title = t('jobs-management.voucher-not-found-title')
        modalProps.description = t('jobs-management.voucher-not-found-detail')
      }
    }
    showCommonModal(modalProps)
  }, [])

  const onCloseModalAssign = useCallback(({ query, path }: { query?: any; path?: string }) => {
    if (query) {
      setInhibitLeaveModalSpecialCondition(true)
      router.push({
        pathname: path,
        query,
      })
    }
    fetchJobCount()
    hideCommonModal()
  }, [])

  // --- SUB COMPONENT --- //
  const renderJobDetail = useMemo(() => {
    // console.log('renderJobDetail render')
    if (!job) return <></>
    const {
      deviceKey,
      deviceKey2,
      requestedAt,
      modelIdentifiers,
      shopUserName,
      adminUserName,
      branch,
      estimatedAt,
      rejectedAt,
      completedShopAt,
    } = job
    const { brand, model, rom } = modelIdentifiers

    const arrangeDetail: DisplayStaticData[] = [
      { title: t('common.jobs-management.brand'), displayValue: brand },
      { title: t('common.jobs-management.model'), displayValue: model },
      { title: t('common.jobs-management.rom'), displayValue: rom },
      { title: t('common.jobs-management.device-key'), displayValue: deviceKey },
      { title: t('common.jobs-management.device-key2'), displayValue: deviceKey2 },
      { title: '', displayValue: 'blank' },
      { title: t('common.jobs-management.branch-name'), displayValue: `${branch.branchId} - ${branch.title}` },
      { title: t('common.jobs-management.shop-staff'), displayValue: shopUserName },
      { title: t('common.jobs-management.estimate-staff'), displayValue: adminUserName },
    ]

    if (job.status === JobStatus.REJECT_BY_SHOP) {
      arrangeDetail.push({
        title: t('common.jobs-management.shop-cancel-date'),
        displayValue: rejectedAt,
        isDate: true,
      })
    } else {
      arrangeDetail.push({
        title: t('common.jobs-management.job-requested-time'),
        displayValue: requestedAt,
        isDate: true,
      })
    }

    if (
      ![
        JobStatus.DRAFT,
        JobStatus.QUOTE_REQUESTED,
        JobStatus.ESTIMATE_PRICE_PROCESSING,
        JobStatus.REJECT_BY_SHOP,
      ].includes(job.status as JobStatus)
    ) {
      arrangeDetail.push({
        title: t('common.jobs-management.estimate-time'),
        displayValue: estimatedAt,
        isDate: true,
      })
    }

    if ([JobStatus.PURCHASED, JobStatus.REJECT_BY_CUSTOMER, JobStatus.RECEIVED].includes(job.status as JobStatus)) {
      arrangeDetail.push({
        title: t('common.jobs-management.completed-shop'),
        displayValue: completedShopAt,
        isDate: true,
      })
    }

    return (
      <div className="p-6 border-2 rounded-b-lg bg-base-25 ">
        {/* Title */}
        <h1 className="mb-4 text-primary-500 text-h4-bold">{t('common.jobs-management.device-detail')}</h1>
        {/* Details */}
        <div className={`grid grid-cols-3 gap-x-6 gap-y-4`}>
          {arrangeDetail.map(data => {
            return (
              <div key={data.title}>
                {data.displayValue !== 'blank' ? (
                  <div key={data.title}>
                    <p className="text-b5-regular text-base-500">{data.title}</p>
                    <p className="text-bh3-semi-bold text-base-700">
                      {data.isDate ? defaultValue(displayIsDate(data.displayValue)) : defaultValue(data.displayValue)}
                    </p>
                  </div>
                ) : (
                  <p className="text-bh3-semi-bold text-base-700"></p>
                )}
              </div>
            )
          })}
        </div>
      </div>
    )
  }, [jobAtMount])

  // --- PERMISSION --- //
  const permissionEdit = useMemo(() => {
    return permissionConfig?.[PermissionMapCode.CMS_JOB_MY]?.[PermissionAction.UPDATE] ?? false
  }, [permissionConfig])
  // --- RETURN MAIN COMPONENT--- //
  return (
    <>
      <LayoutPage
        fixFooter={
          <div className="flex flex-col items-end w-[100%]">
            {/* Comments Icon */}
            {user?.userKey === jobAtMount?.adminUserKey && !onNotiFromDifferenceID && (
              <button
                className={`${
                  !jobAtMount?.estimatedAt && `${PermissionMapCode.CMS_JOB_MY}-update-permission`
                } flex flex-col items-center justify-center bg-primary-500 w-[104px] h-[102px] rounded-lg p-2 cursor-pointer absolute right-6 top-[-126px]`}
                onClick={() => {
                  setIsOpenComment(true)
                  setLastOpenedCommentInMilliseconds(jobId, new Date().getTime())
                }}
              >
                <div className="!w-16 !h-16 m-1 icons ic-admin-comment" />
                <p className="mt-1 text-white text-bh6-semi-bold">{t('ask-admin')}</p>
                {hasUnreadComment && <div className="absolute w-6 h-6 rounded-full -top-2 -right-2 bg-negative-500" />}
              </button>
            )}

            {job?.vendorType === VendorTypeEnum.kf ? (
              <PriceFooterKF
                dataAtMount={jobAtMount}
                calculatedPrice={calculatedPrice}
                getPrice={(price, isManual) => setSelectedPrice({ price, isManual })}
                onSubmit={(price, grade) => {
                  openConfirmSuggestPriceModal(price, grade, job as Job)
                }}
                permissionEdit={permissionEdit}
              />
            ) : (
              <PriceFooter
                dataAtMount={jobAtMount}
                calculatedPrice={calculatedPrice}
                tick={100}
                getPrice={(price, isManual) => setSelectedPrice({ price, isManual })}
                onSubmit={(price, grade) => {
                  openConfirmSuggestPriceModal(price, grade, job as Job)
                }}
                permissionEdit={permissionEdit}
              />
            )}
          </div>
        }
        beforeLogoutFn={
          (fromId && isOwner) || (user?.userKey === jobAtMount?.adminUserKey && jobAtMount?.suggestedPrice === 0)
            ? logoutFn => confirmLeaveModal(logoutFn)
            : undefined
        }
      >
        <div className="w-full h-full">
          {/* header section */}
          <div className="relative flex flex-row justify-between">
            <div className="flex flex-col flex-1">
              {router.pathname.split('/')[2] === 'detail' && (
                <Breadcrumb
                  fixedCrumbs={crumbs}
                  route={router}
                  separatorIcon={<i className="w-6 h-6 icons ic-chevron-gray-right" />}
                  textMap={urlToTextMap}
                  modifyCrumbs={modifyCrumbs}
                  t={t}
                />
              )}
              {router.pathname.split('/')[2] === 'history' && (
                <div>
                  <Link
                    href={`/${router.pathname.split('/')[1]}/detail?id=${fromId}`}
                    className="text-primary-500 text-t4-semi-bold inline-flex"
                  >
                    <div className="icons ic-chevron-back-blue" />
                    <div className="ml-2">{t('common.back')}</div>
                  </Link>
                </div>
              )}

              <PageTitle title={t(pageTitle)} />
            </div>
            {jobAtMount?.status === JobStatus.QUOTE_REQUESTED && (
              <Button
                colorScheme="primary"
                onClick={onClickAssignToMe}
                cls={`h-12 ${PermissionMapCode.CMS_JOB_ALL}-update-permission`}
              >
                <>
                  <i className="icons ic-task-document-white mr-2" />
                  {t('common.data-table.accept-evaluation')}
                </>
              </Button>
            )}
            {jobAtMount?.status === JobStatus.ESTIMATE_PRICE_PROCESSING && historyId !== null && (
              <Button colorScheme="primary" variant="outlined" onClick={navigateToHx} cls="h-12">
                <>
                  <i className="icons ic-clock-blue mr-2" />
                  {t('common.jobs-management.history-check')}
                </>
              </Button>
            )}
          </div>
          {/* body section */}
          <div className="flex flex-col items-center h-full">
            {/* First Section */}
            <div className="w-full mb-4">
              <div className="flex items-center justify-between p-4 border-2 border-b-0 rounded-t-lg bg-base-25">
                <div className="flex flex-row items-center">
                  <p className="text-bh2-semi-bold text-base-500">{t('jobs-management.id')}</p>
                  <span className="ml-2 text-t1-semi-bold">{router.query?.id}</span>
                </div>
                {job?.status && getStatusFromCodeStatus(job?.status) && getColorChipFromCodeStatus(job?.status) && (
                  <Chip color={getColorChipFromCodeStatus(job?.status)}>
                    {t(`common.status.${getStatusFromCodeStatus(job?.status)}`)}
                  </Chip>
                )}
              </div>
              {renderJobDetail}
            </div>

            {/* Dynamic Section */}
            {job?.checkList?.length &&
              job.checkList.map(template => {
                const slug = template.slug
                const answer = job?.checkListValues?.[slug]
                if (!answer) return <div key={slug}></div>
                if (slug === PRODUCT_IMAGES) {
                  template.title_detail = t(`jobs-management.image-list`, { imageCount: Object.keys(answer).length })

                  return <SurveyViewDetail key={slug} template={template} answer={answer} />
                }

                if (slug === REMOBIE_SLUG_NAME) {
                  // verify remobie template is older version or not
                  const isSupportLegacyTemplate = template?.survey_form.pages[0].elements.some(
                    (item: any) => item.name === 'condition_radio_group'
                  )

                  if (isSupportLegacyTemplate) {
                    return (
                      <Fragment key={slug}>
                        <OverwriteSurveyRemobieCheckList getRemobieChecklist={setRemobieChecklist} />
                      </Fragment>
                    )
                  } else {
                    return <SurveyViewDetail key={slug} template={template} answer={answer} clsContainer={slug} />
                  }
                }

                if (slug === ADDITIONAL_PROD_INFO) {
                  if (
                    (user?.userKey === job.adminUserKey && job.status === JobStatus.ESTIMATE_PRICE_PROCESSING) ||
                    job.status === JobStatus.QUOTE_REQUESTED
                  ) {
                    return (
                      <div className="w-full p-6 mb-4 border-2 rounded-lg bg-base-25" key={slug}>
                        <div className="flex items-center justify-between">
                          <div className="flex flex-row items-center gap-2 text-primary-500 text-h4-bold  ">
                            <p>{additionalSurveyTitleName}</p>
                          </div>
                        </div>

                        <div className="mt-4">
                          <Survey model={additionalSurvey} onValueChanged={onSurveyValueChanged} />
                        </div>
                      </div>
                    )
                  } else {
                    const newAnswer = job.adminCheckListValues?.additional_product_information || answer
                    return <SurveyViewDetail key={slug} template={template} answer={newAnswer} />
                  }
                }

                if (slug === PRODUCT_INFORMATION) {
                  const isNewTemplate = template?.survey_form.pages[0].elements.some((item: any) =>
                    (item.type || '').includes('question_')
                  )

                  if (isNewTemplate) {
                    return (
                      <OverwriteQuestion
                        key={slug}
                        job={job}
                        jobAtMount={jobAtMount}
                        questionSurvey={template}
                        shopAnswer={answer}
                        onChange={result => setQuestionChecklist(result)}
                        surveyViewComponent={(answer: any) => (
                          <SurveyViewDetail
                            key={slug}
                            template={template}
                            answer={answer}
                            clsContainer={`${slug} border-none px-0 pb-0`}
                            hideTitle={true}
                            setPopupClueModalConfig={setPopupClueModalConfig}
                          />
                        )}
                        failAfterSubmit={questionValidateFailAfterSubmit}
                        clearFailAfterSubmit={() => setQuestionValidateFailAfterSubmit(false)}
                        setPopupClueModalConfig={setPopupClueModalConfig}
                        permissionEdit={permissionEdit}
                      />
                    )
                  }
                  const { device_condition, ...ansProductInfo } = answer
                  return <SurveyViewDetail key={slug} template={template} answer={ansProductInfo} clsContainer={slug} />
                }

                if (slug === PRODUCT_ADDITIONAL_IMAGES) {
                  if (Object.keys(answer).length === 0) return <div key={slug}></div>

                  const productImagesTemplate = job.checkList.find(i => i.slug == PRODUCT_ADDITIONAL_IMAGES)

                  productImagesTemplate?.survey_form.pages[0]?.elements.forEach((item: any) => {
                    item.title = item.title.replaceAll(
                      t(`jobs-management.take-additional-photo`),
                      t(`jobs-management.additional-image`)
                    )
                  })

                  template.title = t(`jobs-management.additional-image-list`, {
                    imageCount: answer['additional-image'].length,
                  })

                  return (
                    <SurveyViewDetail
                      key={slug}
                      template={productImagesTemplate || template}
                      answer={answer}
                      clsContainer={slug}
                    />
                  )
                }

                return <SurveyViewDetail key={slug} template={template} answer={answer} clsContainer={slug} />
              })}
          </div>
        </div>
      </LayoutPage>
      {/* ----------------------------------------- Comment Drawer ----------------------------------------- */}
      <CommentDrawer
        isOpenComment={isOpenComment}
        onClose={() => {
          setLastOpenedCommentInMilliseconds(jobId, new Date().getTime())
          if (router.query?.comment) {
            const { comment, ...updatedQuery } = router.query
            router.replace({ pathname: router.pathname, query: updatedQuery }, undefined, { shallow: true })
          } else {
            setIsOpenComment(false)
          }
        }}
        commentConversation={commentConversation}
        job={job}
      />
    </>
  )
}
