import { useMemo } from 'react'
// Import Custom Hook
import { useTranslation } from '@/hooks'

// Import Config

import { ConnectionState, Sorting, formatPrice } from 'utils'
// Import Component
import { DataTableField, DataTableWithPagination as DataTable, Chip } from 'ui'
import { useUploadVoucherStore, defaultImportVoucherSorting } from '@/stores/uploadVoucherStore'

import { GetCountVocuherResponse } from 'contracts'
import { getStatusFromImportedVoucherStatus, getColorChipFromImportedVoucherStatus } from '@/utils'

// -------- INTERFACE-------- //
export interface ITableDOProps {
  activateFetch: ({ page, sorting }: { page?: number; sorting?: Sorting }) => void
}

// -------- CONST -------- //
const emptyState = {
  title: 'imported-voucher-table.table-not-found-title',
  image: 'admin-empty-table',
  detail: 'imported-voucher-table.table-not-found-description',
}

const getHeaderFields = (t: (text: string) => string): DataTableField[] => {
  const list = [
    {
      name: 'providerName',
      label: t('imported-voucher-table.provider-name'),
      width: '160px',
      textAlignment: 'text-left justify-start',
      formatter: (item: GetCountVocuherResponse) => {
        return <>{item.providerName}</>
      },
    },
    {
      name: 'total',
      label: t('imported-voucher-table.total-voucher'),
      width: '150px',
      textAlignment: 'text-center justify-center',
      disableSort: true,
      cls: 'text-d6-semi-bold text-primary-500',
      formatter: (item: GetCountVocuherResponse) => {
        return <>{formatPrice(item.total)}</>
      },
    },
    {
      name: 'used',
      label: t('imported-voucher-table.used-voucher'),
      width: '150px',
      textAlignment: 'text-center justify-center',
      cls: 'text-d6-semi-bold text-primary-500',
      formatter: (item: GetCountVocuherResponse) => {
        return <>{formatPrice(item.used)}</>
      },
    },
    {
      name: 'available',
      label: t('imported-voucher-table.remain-voucher'),
      width: '150px',
      textAlignment: 'text-center justify-center',
      cls: 'text-d6-semi-bold text-primary-500',
      formatter: (item: GetCountVocuherResponse) => {
        return <>{formatPrice(item.available)}</>
      },
    },
    {
      name: 'voucherValue',
      label: t('imported-voucher-table.voucher-value'),
      width: '150px',
      textAlignment: 'text-right justify-end',
      cls: 'text-d6-semi-bold text-primary-500',
      formatter: (item: GetCountVocuherResponse) => {
        return <>{formatPrice(item.voucherValue, 2)}</>
      },
    },
    {
      name: 'total',
      label: t('imported-voucher-table.voucher-status'),
      width: '150px',
      textAlignment: 'text-center justify-center',
      disableSort: true,
      formatter: (item: GetCountVocuherResponse) => {
        return (
          <>
            {
              <Chip color={getColorChipFromImportedVoucherStatus(Number(item.available) === 0 ? 'EMPTY' : 'REMAIN')}>
                {t(
                  `imported-voucher-table.${getStatusFromImportedVoucherStatus(
                    Number(item.available) === 0 ? 'EMPTY' : 'REMAIN'
                  )}`
                )}
              </Chip>
            }
          </>
        )
      },
    },
  ]
  const toReturn: DataTableField[] = [...list]

  return toReturn
}

export function ImportedVoucherCountTable({ activateFetch }: Readonly<ITableDOProps>) {
  const { t } = useTranslation('common')
  const {
    impotedVoucherCountTableData,
    impotedVoucherCountConnectionState: connectionState,
    impotedVoucherCountTablePagination: pagination,
    impotedVoucherCountSorting: sorting,
    setImpotedVoucherCountTablePagination: setPagination,
    setImpotedVoucherCountTableSorting: setSorting,
  } = useUploadVoucherStore()

  const headerComponent = useMemo(() => {
    return getHeaderFields(t)
  }, [])

  const setPaginationPage = (page: number) => {
    setPagination(page, 'page')
    activateFetch({ page })
  }

  const setSortingField = (by: string | null, direction: 'asc' | 'desc' | undefined) => {
    setSorting(by, direction)
    activateFetch({ sorting: { by, direction } })
  }

  return (
    <DataTable
      fields={headerComponent}
      data={impotedVoucherCountTableData}
      isError={ConnectionState.isError(connectionState)}
      isLoading={ConnectionState.isLoading(connectionState)}
      errorView={{
        onRefresh: () => activateFetch({ page: 1 }),
      }}
      heightOverScreen={true}
      cls={`!static ${ConnectionState.isLoading(connectionState) ? 'min-h-[470px]' : ''}`}
      t={t}
      emptyState={emptyState}
      pagination={pagination}
      setPaginationPage={setPaginationPage}
      sorting={sorting}
      defaultSortingField={defaultImportVoucherSorting}
      setSorting={setSortingField}
    />
  )
}
