import { ReactNode, useCallback, useEffect, useLayoutEffect, useState } from 'react'
import { useTranslation } from '@/hooks'
import { useSystemStore } from '@/stores/systemStore'
import { useBranchesStore } from '@/stores/branchesStore'

import { DropdownSimple } from 'ui'
import { SelectBranchDropdown } from './SelectBranchDropdown'
import { DefaultAOBranchDetail } from './createAO'
import { BranchType } from '@/config/branches'
import { AllocationOrderType } from 'contracts'

const ORIGIN_BRANCHS = [BranchType.SHOP, BranchType.WAREHOUSE]
const DESTINATION_BRANCHS = [BranchType.SHOP, BranchType.WAREHOUSE, BranchType.DEALER]
export interface ISelectBranchProps {
  onChange?: (data: { origin: IDropdownChoice; destination: IDropdownChoice; type: IDropdownChoice }) => void
}
type IDropdownChoice = { text: string; value: string }
type IFocusDropdown = 'origin' | 'destination' | 'type' | null
export interface AOBranchDetail {
  type: IDropdownChoice
  origin: IDropdownChoice
  destination: IDropdownChoice
}

const brachDisplay = (icon: ReactNode, title: string, select: string, selectPlaceholder: string) => {
  return (
    <div className="flex items-center justify-between p-4 shadow-md shadow-[#3B5998]/10 rounded-b-lg">
      {icon}
      <div className="w-full">
        <p className="text-t5-semi-bold text-left">{title}</p>
        <p className="text-b4-regular text-base-500 text-left">{select || selectPlaceholder}</p>
      </div>
    </div>
  )
}

export function SelectBranch({ onChange }: ISelectBranchProps) {
  const { fetchBranchesTableData, branchesTableData } = useBranchesStore()
  const { t } = useTranslation('common')
  const { isShowMenu } = useSystemStore()
  const [selectedDropdown, setSelectedDropdown] = useState<AOBranchDetail>(DefaultAOBranchDetail({ t }))
  const [focusDropdown, setFocusDropdown] = useState<IFocusDropdown>(null)
  const [mountDropdown, setMountDropdown] = useState<boolean>(true)

  useEffect(() => {
    if (branchesTableData.length === 0) {
      fetchBranchesTableData({ page: '1' }, { pagination: 'false' })
    }
  }, [])

  useEffect(() => {
    if (onChange) {
      onChange(selectedDropdown)
    }
  }, [selectedDropdown])

  useEffect(() => {
    if (mountDropdown === false) {
      setMountDropdown(true)
    }
  }, [mountDropdown])

  useEffect(() => {
    setMountDropdown(false)
  }, [isShowMenu])

  useLayoutEffect(() => {
    const reRenderDropdown = () => setMountDropdown(false)
    window.addEventListener('resize', reRenderDropdown)
    return () => window.removeEventListener('resize', reRenderDropdown)
  }, [])

  const onClickDropdown = useCallback(
    (dropdown: IFocusDropdown, toStatus: boolean) => {
      if (dropdown === focusDropdown && toStatus === false) {
        setFocusDropdown(null)
      } else if (dropdown !== focusDropdown && toStatus) {
        setMountDropdown(false)
        setFocusDropdown(dropdown)
      }
    },
    [focusDropdown]
  )

  return (
    <div className="mt-4 px-6 py-6 bg-white rounded-lg">
      <p className="text-h4-bold text-primary-500 mb-4">{t('create-allocation-order.ao-data')}</p>
      <p className="text-b4-regular text-base-700">
        {t('create-allocation-order.branch-data')}
        <span className="text-negative-500"> *</span>
      </p>
      <div className="grid gap-6 grid-cols-3 mb-4">
        <SelectBranchDropdown
          choices={branchesTableData
            .filter(branch => ORIGIN_BRANCHS.includes(branch.branchType as BranchType))
            .map(branch => {
              return { text: branch.title, value: branch.branchId }
            })}
          selected={selectedDropdown.origin}
          setSelection={(value: IDropdownChoice) => setSelectedDropdown({ ...selectedDropdown, origin: value })}
          t={t}
          absoluteChoice={true}
          parentLeftPx={isShowMenu ? 265 : 72}
          searchable={true}
          searchPlaceholder={t('create-allocation-order.find-branch')}
          displaySection={selected =>
            brachDisplay(
              <div className="flex items-center justify-center mr-2 rounded-lg w-11 h-11 bg-info-100">
                <i className="icons ic-location-pin-blue !w-[36px] !h-[36px]" />
              </div>,
              t('create-allocation-order.origin'),
              t(selected.text),
              t('create-allocation-order.select-origin-branch')
            )
          }
          onClickDropdown={toStatus => onClickDropdown('origin', toStatus)}
          isOpenDropdown={focusDropdown === 'origin'}
        />
        <SelectBranchDropdown
          choices={branchesTableData
            .filter(branch => DESTINATION_BRANCHS.includes(branch.branchType as BranchType))
            .map(branch => {
              return { text: branch.title, value: branch.branchId }
            })}
          selected={selectedDropdown.destination}
          setSelection={(value: IDropdownChoice) => setSelectedDropdown({ ...selectedDropdown, destination: value })}
          t={t}
          absoluteChoice={true}
          parentLeftPx={isShowMenu ? 265 : 72}
          searchable={true}
          searchPlaceholder={t('create-allocation-order.find-branch')}
          displaySection={selected =>
            brachDisplay(
              <div className="flex items-center justify-center mr-2 rounded-lg w-11 h-11 bg-negative2-100">
                <i className="icons ic-location-pin-pink !w-[36px] !h-[36px]" />
              </div>,
              t('create-allocation-order.destination'),
              t(selected.text),
              t('create-allocation-order.select-destination-branch')
            )
          }
          onClickDropdown={toStatus => onClickDropdown('destination', toStatus)}
          isOpenDropdown={focusDropdown === 'destination'}
        />
      </div>
      <div className="grid grid-cols-3">
        {selectedDropdown.type && mountDropdown && (
          <DropdownSimple
            choices={[
              { text: t('create-allocation-order.retail'), value: AllocationOrderType.RETAIL },
              { text: t('create-allocation-order.wholesale'), value: AllocationOrderType.WHOLESALE },
            ]}
            selected={selectedDropdown.type}
            setSelection={(value: IDropdownChoice) => setSelectedDropdown({ ...selectedDropdown, type: value })}
            t={t}
            label={t('create-allocation-order.type')}
            isRequire={true}
            absoluteChoice={true}
            parentLeftPx={isShowMenu ? 265 : 72}
            onClickDropdown={toStatus => onClickDropdown('type', toStatus)}
            mountDropdownOpen={focusDropdown === 'type'}
          />
        )}
        {!(selectedDropdown.type && mountDropdown) && <div className="h-[70px]"></div>}
      </div>
    </div>
  )
}
