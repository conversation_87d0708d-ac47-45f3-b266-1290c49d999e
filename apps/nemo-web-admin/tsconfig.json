{"extends": "tsconfig/nextjs.json", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./*"], "nemo-common": ["../../packages/common"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "../../types/**/*.d.ts"], "exclude": ["node_modules"]}