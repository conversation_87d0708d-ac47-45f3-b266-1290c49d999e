import 'survey-core/defaultV2.min.css'
import { withAuth } from '@/hocs'
import 'ui/src/survey/ActionItems'
import { JobDetail } from '@/components/jobDetail'
import 'ui/src/survey/PreviewUploadFile'
import { useRouter } from 'next/router'

function MyJobsDetailPage() {
  const router = useRouter()
  const jobId = (router.query.id as string) ?? ''

  return (
    <JobDetail
      jobId={jobId}
      pageType={router.pathname.split('/')[1] || 'PENDING'}
      pageTitle={'common.my-jobs'}
    ></JobDetail>
  )
}

export default withAuth(MyJobsDetailPage)
