{"name": "nemo-web-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8001", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "test": "jest", "test:coverage": "jest --coverage --detectOpenHandles --config=jest.config.js __tests__/*"}, "dependencies": {"@material-tailwind/react": "^2.1.4", "cerialize": "^0.1.18", "next": "^14.2.25", "react": "^18", "react-dom": "^18", "ui": "*", "utils": "*", "nemo-common": "*", "zustand": "^4.4.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "config": "*", "contracts": "*", "eslint": "*", "eslint-config-custom": "*", "eslint-config-next": "13.5.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-router-mock": "^0.9.10", "typescript": "^5"}}