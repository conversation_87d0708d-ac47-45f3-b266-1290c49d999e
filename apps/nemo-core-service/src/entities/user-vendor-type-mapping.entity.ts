import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity({ name: 'user_vendor_type_mapping' })
export class UserVendorTypeMappingEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 30 })
  userType!: string;

  @PrimaryColumn({ type: 'varchar', length: 30 })
  vendorType!: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  providerName?: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  discountProviderName?: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  partnerName?: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  carrierProgramCode?: string;
}
