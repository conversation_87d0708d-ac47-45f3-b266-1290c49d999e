import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  <PERSON>in<PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { ModelMasterEntity } from './model-master.entity';
import { ColumnDecimalTransformer } from './transformer/column-decimal.transformer';
import { JobActivitiesEntity } from './job-activities.entity';
import { ContractEntity } from './contract.entity';
import { EncryptDecryptString } from './decorators';
import { AutoIdField } from '../utils/autoId';
import { BranchEntity } from './branch.entity';
import { UserEntity } from './user.entity';
import { DeliveryOrderEntity } from './delivery-order.entity';
import { AllocationOrderEntity } from './allocation-order.entity';
import { EstimationActivitiesEntity } from './estimation-activities.entity';
import { ModelMasterColorEntity } from './model-master-color.entity';
import { CampaignRedemptionCodeEntity } from './campaign-redemption-code.entity';
import { CampaignEntity } from './campaign.entity';
import { IssueReportEntity } from './issue-report.entity';

// Done state is 40_PURCHASED, 98_REJECT_BY_SHOP, '99_REJECT_BY_CUSTOMER',
export enum JobStatus {
  DRAFT = '00_DRAFT',
  QUOTE_REQUESTED = '10_QUOTE_REQUESTED',
  ESTIMATE_PRICE_PROCESSING = '11_ESTIMATE_PRICE_PROCESSING',
  PRICE_ESTIMATED = '12_PRICE_ESTIMATED',
  IDENTITY_REQUESTED = '20_IDENTITY_REQUESTED',
  IDENTITY_REJECTED = '21_IDENTITY_REJECTED',
  IDENTITY_VERIFIED = '30_IDENTITY_VERIFIED',
  CAMPAIGN_SELECTED = '35_CAMPAIGN_SELECTED',
  PURCHASED = '40_PURCHASED',
  RECEIVED = '50_RECEIVED',
  QC_COMPLETED = '60_QC_COMPLETED',
  REPAIR_ASSIGNED = '65_REPAIR_ASSIGNED',
  REPAIR_COMPLETED = '70_REPAIR_COMPLETED',
  INSPECTION_ASSIGNED = '75_INSPECTION_ASSIGNED',
  INSPECTION_FAILED = '79_INSPECTION_FAILED',
  INSPECTION_COMPLETED = '80_INSPECTION_COMPLETED',
  INSPECTION_AUTO_COMPLETED = '81_INSPECTION_AUTO_COMPLETED',
  REJECT_BY_SHOP = '98_REJECT_BY_SHOP',
  REJECT_BY_CUSTOMER = '99_REJECT_BY_CUSTOMER',
  AO_CREATED = '82_AO_CREATED',
  AO_RECEIVED = '83_AO_RECEIVED',
  SOLD = '89_SOLD',
}

export enum JobShippingStatus {
  SHIPPED = '00_SHIPPED',
  RECEIVED = '10_RECEIVED',
  RECEIVED_WITH_CONDITION = '11_RECEIVED_WITH_CONDITION',
  RECEIVED_OTHER = '12_RECEIVED_OTHER',
}

export enum AOShippingStatus {
  SHIPPED = '00_SHIPPED',
  RECEIVED = '10_RECEIVED',
  NOT_SHIPPED = '91_NOT_SHIPPED',
  NOT_SCANNED = '92_NOT_SCANNED',
  LOST = '93_LOST',
}

export enum QCStatus {
  FIX = 'fix',
  REFURBISH = 'refurbish',
  SCRAP = 'scrap',
}

export type IRepairHxGrade = 'A' | 'B' | 'C' | 'D';
export type AOShippingStatusFail =
  | AOShippingStatus.NOT_SHIPPED
  | AOShippingStatus.NOT_SCANNED
  | AOShippingStatus.LOST;
export interface RepairHx {
  type: QCStatus.FIX | QCStatus.REFURBISH | QCStatus.SCRAP;
  detail: string;
  cost?: number;
  grade?: IRepairHxGrade;
  by: { key: string; name: string };
  at: Date;
  assignedAt?: Date;
}

export interface InspectHx {
  by: { key: string; name: string };
  at: Date;
  detail?: string;
  isPassed: boolean;
  isQCScrap: boolean;
}

export interface UpdateCostHx {
  retailPrice: number;
  wholeSalePrice: number;
  by: { key: string; name: string };
  at: Date;
}

export interface AOListValue {
  allocationOrderId: string;
  userKey: string;
  userName: string;
  remark: string;
  type: AOShippingStatusFail;
  confirmedAt: Date;
  videoPath?: string;
}

@Entity({ name: 'job' })
export class JobEntity extends BaseEntity {
  @Index()
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Index({
    unique: true,
  })
  @PrimaryColumn({ type: 'varchar', length: 200 })
  jobId: string = AutoIdField.basic.produce();

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: 'IMEI value',
  })
  deviceKey?: string | null;

  @Column({
    type: 'varchar',
    length: 200,
    comment: 'IMEI2 value',
    nullable: true,
  })
  deviceKey2?: string | null;

  @Index()
  @Column({ type: 'varchar', length: 200 })
  branchId!: string;

  @Column({ type: 'varchar', length: 200 })
  modelKey!: string;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  colorId?: string | null;

  @Column({ type: 'varchar', length: 30, nullable: true })
  thaiId?: string;

  @EncryptDecryptString({ type: 'text', nullable: true })
  phoneNumber?: string;

  @Column({ type: 'varchar', length: 30, default: 'MASS' })
  vendorType!: string;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  updatedBy?: string;

  @Column({ name: 'requested_at', type: 'timestamptz', nullable: true })
  requestedAt?: Date;

  @Column({ name: 'assigned_at', type: 'timestamptz', nullable: true })
  assignedAt?: Date;

  @Column({ name: 'estimated_at', type: 'timestamptz', nullable: true })
  estimatedAt?: Date;

  @Column({ name: 'purchased_at', type: 'timestamptz', nullable: true })
  purchasedAt?: Date;

  @Column({ name: 'rejected_at', type: 'timestamptz', nullable: true })
  rejectedAt?: Date;

  @Column({ name: 'completed_shop_at', type: 'timestamptz', nullable: true })
  completedShopAt?: Date;

  @Index()
  @Column({ type: 'varchar' })
  status!: JobStatus;

  @Column({ type: 'jsonb' })
  modelIdentifiers!: any;

  @Column({ type: 'jsonb' })
  modelTemplate!: ModelMasterEntity;

  @Column({ type: 'jsonb', nullable: true })
  penalties?: any;

  @Column({ type: 'jsonb' })
  checkList!: any[];

  @Column({ type: 'jsonb' })
  checkListValues!: any;

  @Column({ type: 'jsonb', nullable: true })
  adminCheckListValues?: any;

  @Column({
    type: 'boolean',
    nullable: true,
    default: false,
  })
  isAdditionalCheckList?: boolean;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  suggestedPrice?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  purchasedPrice?: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  currentGrade?: string | null;

  @Column({ type: 'varchar', length: 200 })
  shopUserKey!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  shopUserName?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  adminUserKey?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  adminUserName?: string;

  @Column({
    name: 'delivery_order_id',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  deliveryOrderId?: string;

  @Column({ type: 'varchar', nullable: true })
  shippingStatus?: JobShippingStatus;

  @Column({ type: 'varchar', length: 200, nullable: true })
  receiverUserKey?: string;

  @Column({ type: 'varchar', nullable: true })
  receivingRemark?: string;

  @Column({ name: 'received_at', type: 'timestamptz', nullable: true })
  receivedAt?: Date;

  @Column({
    name: 'allocation_order_id',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  allocationOrderId?: string | null;

  @Column({ type: 'varchar', nullable: true })
  aoShippingStatus?: AOShippingStatus | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  aoReceiverUserKey?: string;

  @Column({ type: 'varchar', nullable: true })
  aoReceivingRemark?: string;

  @Column({ name: 'ao_received_at', type: 'timestamptz', nullable: true })
  aoReceivedAt?: Date;

  @Column({ type: 'varchar', length: 200, nullable: true })
  qcBy?: string;

  @Column({ name: 'qc_at', type: 'timestamptz', nullable: true })
  qcAt?: Date;

  @Column({ type: 'varchar', length: 200, nullable: true })
  repairedBy?: string;

  @Column({ name: 'repaired_at', type: 'timestamptz', nullable: true })
  repairedAt?: Date;

  @Column({ name: 'assign_repair_at', type: 'timestamptz', nullable: true })
  assignRepairAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  repairListValue?: RepairHx[];

  @Column({ type: 'varchar', nullable: true })
  qcStatus?: QCStatus;

  @Column({ type: 'varchar', length: 200, nullable: true })
  inspectedBy?: string | null;

  @Column({ name: 'inspected_at', type: 'timestamptz', nullable: true })
  inspectedAt?: Date;

  @Column({ name: 'assign_inspect_at', type: 'timestamptz', nullable: true })
  assignInspectAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  inspectListValue?: InspectHx[];

  @Column({ type: 'varchar', length: 10, nullable: true })
  estimatedGrade?: string | null;

  // ไว้เทียบกับ estimationOrderId step create ว่าเป็นค่าเดียวกัน (ไม่ผูก entity)
  @Column({ type: 'varchar', length: 200, nullable: true })
  draftEstimationId?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  confirmedPriceBy?: string | null;

  @Column({ name: 'confirmed_price_at', type: 'timestamptz', nullable: true })
  confirmedPriceAt?: Date;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  costPrice?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  retailPrice?: number | null;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  wholeSalePrice?: number | null;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  retailMargin?: number | null;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  wholeSaleMargin?: number | null;

  @Column({
    type: 'decimal',
    precision: 7,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  marginRetailBaht?: number | null;

  @Column({
    type: 'decimal',
    precision: 7,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  marginWholeSaleBaht?: number | null;

  @Column({ type: 'jsonb', nullable: true })
  adminUpdateCostListValue?: UpdateCostHx[];

  @Column({ type: 'varchar', length: 200, nullable: true })
  depositContractLink?: string;

  @Column({ type: 'bool', default: false })
  isConfirmPrice!: boolean;

  @Column({ name: 'incomplete_ao_list_value', type: 'jsonb', nullable: true })
  incompleteAOListValue?: AOListValue[];

  @Column({ type: 'varchar', length: 10, nullable: true })
  shopGrade?: string | null;

  @Column({ type: 'boolean', nullable: true })
  isKingfisherEligible?: boolean | null;

  @Column({ type: 'varchar', length: 30, nullable: true })
  partnerName?: string | null;

  @Column({ type: 'varchar', length: 30, nullable: true })
  saleCode?: string | null;

  @Column({ type: 'varchar', length: 30, nullable: true })
  offerReference?: string | null;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'model_key',
      referencedColumnName: 'modelKey',
    },
  ])
  @ManyToOne(() => ModelMasterEntity, (model) => model.jobs, {
    onDelete: 'CASCADE',
  })
  modelMaster!: ModelMasterEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.jobs, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(() => JobActivitiesEntity, (jobActivity) => jobActivity.job, {
    nullable: true,
  })
  jobActivities?: JobActivitiesEntity[];

  @OneToOne(() => ContractEntity, (contract) => contract.job)
  contract?: ContractEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.jobs, {
    onDelete: 'CASCADE',
  })
  branch!: BranchEntity;

  @JoinColumn([
    {
      name: 'shop_user_key',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobsShop)
  shopUser?: UserEntity;

  @JoinColumn([
    {
      name: 'admin_user_key',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobsAdmin)
  adminUser?: UserEntity;

  isUpdatedToEstimated(previousStatus: JobStatus) {
    return (
      previousStatus === JobStatus.ESTIMATE_PRICE_PROCESSING &&
      this.status === JobStatus.PRICE_ESTIMATED
    );
  }

  @JoinColumn([
    {
      name: 'delivery_order_id',
      referencedColumnName: 'deliveryOrderId',
    },
  ])
  @ManyToOne(() => DeliveryOrderEntity, (deliveryOrder) => deliveryOrder.jobs)
  deliveryOrder?: DeliveryOrderEntity;

  @JoinColumn([
    {
      name: 'receiver_user_key',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobsReceiver)
  receiverUser?: UserEntity;

  @JoinColumn([
    {
      name: 'allocation_order_id',
      referencedColumnName: 'allocationOrderId',
    },
  ])
  @ManyToOne(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.jobs,
  )
  allocationOrder?: AllocationOrderEntity;

  @JoinColumn([
    {
      name: 'ao_receiver_user_key',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobsAOReceiver)
  aoReceiverUser?: UserEntity;

  @JoinColumn([
    {
      name: 'qc_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.qcBy)
  qcUser?: UserEntity;

  @JoinColumn([
    {
      name: 'repaired_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.repairedBy)
  repairedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'inspected_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.inspectedBy)
  inspectedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'updated_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobUpdatedBy)
  updatedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.jobCreatedBy)
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'color_id',
      referencedColumnName: 'id',
    },
  ])
  @ManyToOne(
    () => ModelMasterColorEntity,
    (modelMasterColor) => modelMasterColor.jobs,
    {
      nullable: true,
    },
  )
  color?: ModelMasterColorEntity;

  @OneToOne(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.job,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity;

  @JoinColumn([
    {
      name: 'confirmed_price_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.confirmedPriceBy)
  confirmedPriceUser?: UserEntity;

  @OneToMany(
    () => CampaignRedemptionCodeEntity,
    (campaignRedemptionCode) => campaignRedemptionCode.job,
  )
  campaignRedemptionCode?: CampaignRedemptionCodeEntity[];

  @ManyToMany(() => CampaignEntity, { nullable: true })
  @JoinTable()
  campaigns?: CampaignEntity[];

  @ManyToOne(() => IssueReportEntity, (issueReport) => issueReport.job)
  issueReport?: IssueReportEntity[];
}
