import * as bwipjs from 'bwip-js';
import { DateTime } from 'luxon';
import { JobEntity } from '../../../entities/job.entity';
import {
  EmbedFontOptions,
  PDFDocument,
  PDFFont,
  PDFPage,
  PDFPageDrawTextOptions,
  RGB,
  degrees,
  rgb,
} from 'pdf-lib';
import { promises as fs } from 'fs';
import { IS_DEV, defaultValue } from '../..';
import { ModelMasterGradeDetailResponse } from 'contracts';
import { Recipe } from 'muhammara';

export const findCheckListValueChoice = (
  job: JobEntity,
  keyNameProduct: string,
): { text: string; value: string } => {
  let targetSlug;
  const finalCheckListValue = Object.assign(
    {},
    job.checkListValues,
    job.adminCheckListValues,
  );
  const foundChoice = job.checkList
    .flatMap((item) => {
      if (!targetSlug)
        targetSlug =
          finalCheckListValue?.[item.slug]?.[keyNameProduct] !== undefined
            ? finalCheckListValue?.[item.slug]?.[keyNameProduct]
            : '';
      return item.survey_form.pages;
    })
    .flatMap((page) => page.elements)
    .flatMap((element) => {
      if (element.name === 'condition_radio_group') {
        return element?.elements.flatMap((item) => {
          if (item.name === keyNameProduct) {
            return item.choices?.find((choice) => targetSlug === choice.value);
          }
        });
      }
      if (element.name === keyNameProduct) {
        if (element.type === 'module') {
          let result = '-';
          if (targetSlug === 'functional') {
            result = 'ใช้งานได้';
          } else if (targetSlug === 'non_functional') {
            result = 'ใช้งานไม่ได้';
          }
          return { text: result, value: targetSlug };
        }
        if (element.type === 'question_selection') {
          const result = element.choices?.find(
            (choice) => targetSlug === choice.value,
          );
          return { text: result.text?.th, value: targetSlug };
        }
      }
      return [];
    })
    .find((choice) => choice !== undefined);

  return {
    text: foundChoice?.text || '-',
    value: foundChoice?.value,
  };
};

export const isItalicCheckListResult = (value: string): boolean => {
  switch (value) {
    case 'non_functional':
    case 'incomplete':
    case 'abnormal':
    case 'minor_marks':
    case 'major_marks':
    case 'cannot_sign_out':
    case 'below_80_percent':
      return true;
    default:
      return false;
  }
};

// --- file util
export const convertBytesToBase64 = (bytes: Uint8Array): string => {
  const uint8Array = new Uint8Array(bytes);
  let binaryString = '';
  for (const item of uint8Array) {
    binaryString += String.fromCharCode(item);
  }
  // Convert binary string to Base64
  return btoa(binaryString);
};

export const convertBase64ToBytes = (base64String: string): Uint8Array => {
  return Uint8Array.from(Buffer.from(base64String, 'base64'));
};

export const encryptPdf = async (
  pdfInput: Uint8Array,
  key: string,
  password: string,
) => {
  const currentPath = IS_DEV
    ? './src/utils/contract/assets'
    : './dist/src/apps/nemo-core-service/src/utils/contract/assets';

  const inputPath = `${currentPath}/pdf_${key}.pdf`;
  const outputPath = `${currentPath}/pdf_encrypted_${key}.pdf`;

  try {
    // write file pdf from uint8array
    const pdfDoc = await PDFDocument.load(pdfInput);
    const pdfResult = await pdfDoc.save();
    await fs.writeFile(inputPath, pdfResult);

    // encrypt pdf with password
    const pdfEncrypt = new Recipe(inputPath, outputPath);
    pdfEncrypt
      .encrypt({
        userPassword: password,
        userProtectionFlag: 4,
      })
      .endPDF();
    await fs.unlink(inputPath);

    // remove temp pdf
    const encryptedPdf = await fs.readFile(outputPath);
    await fs.unlink(outputPath);
    return encryptedPdf;
  } catch (error) {
    console.error('Error in encryptPdf - ', error);
  }
};

// --- pdf util
export const createTextAdder =
  (page: PDFPage) =>
  (
    text: string,
    font: PDFFont,
    size: number,
    color: RGB,
    x: number,
    y: number,
    italic: boolean = false,
    options: PDFPageDrawTextOptions = { lineHeight: 7 },
  ) => {
    let customOptions = options;
    if (italic)
      customOptions = {
        ...customOptions,
        ySkew: degrees(15),
      };

    page.drawText(text, {
      x,
      y,
      font,
      size,
      color,
      ...customOptions,
    });
  };

export const createRectangleAdder =
  (page: PDFPage) =>
  (
    x: number,
    y: number,
    width: number,
    height: number,
    color?: { bg?: RGB; border?: RGB },
  ) => {
    const bgColor = color?.bg;
    const borderColor = color?.border || color?.bg || rgb(0, 0, 0);
    page.drawRectangle({
      x,
      y,
      width,
      height,
      borderColor,
      borderWidth: 1,
      color: bgColor,
    });
  };

export const embedFont = async (
  pdfDoc: PDFDocument,
  fontPath: string,
  options: EmbedFontOptions = { subset: true },
) => {
  const fontBytes = await fs.readFile(fontPath);
  return pdfDoc.embedFont(fontBytes, options);
};

// --- general util
export const generateBarcode = async (value: string): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    bwipjs.toBuffer(
      {
        bcid: 'code128',
        text: value,
        scaleX: 3,
        width: 160,
      },
      (err, barcodeBuffer) => {
        if (err) {
          console.error('[generate-pdf] - generateAndReturnBarcode', err);
          reject(err);
        } else {
          resolve(barcodeBuffer);
        }
      },
    );
  });
};

// --- data general util
export const dateToDateString = (date: Date, format: string) =>
  DateTime.fromJSDate(new Date(date), {
    zone: 'Asia/Bangkok',
  })
    .reconfigure({ outputCalendar: 'buddhist' })
    .toFormat(format);

export const moneyFormatter = new Intl.NumberFormat('th-TH', {
  style: 'decimal',
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const formatPrice = (price: number) => {
  return (
    price.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }) + ' บาท'
  );
};

// --- data specific util
export const moduleResultByAnswer = {
  functional: { text: 'ใช้งานได้', isNegative: false },
  non_functional: { text: 'ใช้งานไม่ได้', isNegative: true },
  skip: { text: 'ข้าม', isNegative: true },
};

export const getChectListDataBySlug = ({
  job,
  slug,
}: {
  job: JobEntity;
  slug: string;
}) => {
  const { checkList, checkListValues, adminCheckListValues } = job;
  const template = checkList.find((item) => item.slug === slug) ?? null;
  const answerAdmin = adminCheckListValues?.[slug] ?? null;
  const answerShop = checkListValues?.[slug] ?? null;
  const answer = answerAdmin || answerShop;
  const templateData: any = {};
  if (template) {
    const { survey_form: surveyForm } = template;
    const elements = surveyForm.pages.flatMap((page) => page.elements);
    templateData.elements = elements;
  }
  return { template, answer, templateData };
};

export const formatIdCard = (idCard: string) => {
  return idCard.replace(/-/g, ' ');
};

export const formatIdCardNoDash = (idCard: string) => {
  return idCard.replace(/-/g, '');
};

export const formatDateIdCardTh = (dateString: string) => {
  // No expiration date
  if (dateString === '99/99/9999') return 'ตลอดชีพ';
  // No birth date
  else if (dateString.startsWith('00/00/')) {
    return `-  -  ${dateString.slice(-4)}`;
  }
  // Parse the input date using Luxon
  const parsedDate = DateTime.fromFormat(dateString, 'dd/MM/yyyy', {
    zone: 'utc',
  });
  const formattedDate = parsedDate.setLocale('th').toFormat('dd MMM yyyy');

  return formattedDate;
};

export const formatDateIdCardEn = (dateString: string) => {
  // No expiration date

  if (dateString === '99/99/9999') return 'LIFELONG';
  // No birth date
  else if (dateString.startsWith('00/00/')) {
    return `-  -  ${dateString.slice(-4)}`;
  }
  // Parse the input date using Luxon
  const parsedDate = DateTime.fromFormat(dateString, 'dd/MM/yyyy', {
    zone: 'utc',
  });

  const gregorianDate = parsedDate.setLocale('en').toJSDate();
  gregorianDate.setFullYear(gregorianDate.getFullYear() - 543);
  const formattedDate =
    DateTime.fromJSDate(gregorianDate).toFormat('dd MMM. yyyy');

  return formattedDate;
};

export const getMobileGrade = (
  deductPrice: number,
  modelMasterGrades: ModelMasterGradeDetailResponse[],
): string => {
  for (let i = 0; i < modelMasterGrades.length; i++) {
    if (
      deductPrice >= Number(modelMasterGrades[i].purchasePrice) ||
      deductPrice >= Number(modelMasterGrades[i + 1]?.purchasePrice || '1') + 1
    )
      return defaultValue(modelMasterGrades[i].grade, 'D');
  }
  const lastGrade = modelMasterGrades.find(
    (modelMaster) => modelMaster.grade === 'D',
  );
  return defaultValue(lastGrade?.grade, 'D');
};
