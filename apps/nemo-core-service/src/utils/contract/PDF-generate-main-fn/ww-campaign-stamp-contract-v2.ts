import { rgb } from 'pdf-lib';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import { stampImageToNewPdf } from '../PDF-config-general/generate-page';
import { generateBarcode, createTextAdder } from '../PDF-config-general/helper';
import { IS_DEV } from '../..';
import { IPositionStampByPage } from '../../../../src/entities';

export default async function addCampaignCode({
  pdfBase64,
  campaignCodes,
  positions,
  box,
}: {
  pdfBase64: string;
  campaignCodes: { value: string; code: string }[];
  positions: IPositionStampByPage[];
  box: { width: number };
}) {
  try {
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';
    // --- const
    const {
      CAMPAIGN: {
        BOX: { HEIGHT: HEIGHT_BOX },
        BARCODE,
      },
      FONT: { TEXT },
    } = PDF_PAPER_CONTAINER_WW;
    const BOX = { WIDTH: box.width, HEIGHT: HEIGHT_BOX };
    const paddingBarCode = { x: (BOX.WIDTH - BARCODE.WIDTH) / 2, bottom: 32 };
    const textDiffBottom = (TEXT.HEIGHT - TEXT.SIZE) / 2;
    // --- image
    const barcodeImg = await Promise.all(
      campaignCodes.map((code) => generateBarcode(code.code)),
    );

    // --- generate new pdf doc and stamp
    let pdf_base64: string = pdfBase64;
    let pdfDoc_to_save: any = undefined;
    for (let i = 0; i < campaignCodes.length; i++) {
      const {
        position: { x, y },
        page: pageStamp,
      } = positions[i];
      const positionBarcode = {
        x: x + paddingBarCode.x,
        y: y + paddingBarCode.bottom,
      };
      const { value: codeValue, code } = campaignCodes[i];
      const { pdfDoc, font, page } = await stampImageToNewPdf({
        pdfBase64: pdf_base64,
        image: barcodeImg[i],
        configImage: {
          x: positionBarcode.x,
          y: positionBarcode.y,
          width: BARCODE.WIDTH,
          height: BARCODE.HEIGHT,
        },
        currentPath,
        stampOnPage: pageStamp,
      });
      if (i === campaignCodes.length - 1) {
        pdfDoc_to_save = pdfDoc;
      }

      const addText = createTextAdder(page);
      // --- data prompt
      const text = [
        {
          text: 'โปรดเก็บ Barcode นี้ไว้เป็นความลับ',
          font: font.light,
          position: {
            x: x,
            y: y + 4 + textDiffBottom,
          },
        },
        {
          text: `Promotion Code มูลค่า ${codeValue} บาท`,
          font: font.medium,
          position: { x, y: y + 86 + textDiffBottom },
        },
        {
          text: code,
          font: font.medium,
          position: { x, y: y + 18 + textDiffBottom },
        },
      ];
      // --- draw
      text.forEach((item) => {
        const {
          text,
          font,
          position: { x, y },
        } = item;

        const width = font.widthOfTextAtSize(text, TEXT.SIZE);

        addText(
          text,
          font,
          TEXT.SIZE,
          rgb(0, 0, 0),
          x + (BOX.WIDTH - width) / 2,
          y,
        );
      });

      pdf_base64 = await pdfDoc.saveAsBase64({
        objectsPerTick: Infinity,
      });
    }
    // --- save pdf
    if (!pdfDoc_to_save) {
      throw new Error('Failed to add campaign code');
    }
    return await pdfDoc_to_save.save({
      objectsPerTick: Infinity,
    });
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to sign contract ' + error);
  }
}
