import { rgb } from 'pdf-lib';
import { promises as fs } from 'fs';
import {
  findCheckListValueChoice,
  formatDateIdCardEn,
  formatDateIdCardTh,
  formatPrice,
  generateBarcode,
  isItalicCheckListResult,
  getMobileGrade,
} from '../PDF-config-general/helper';
import { JobEntity } from '../../../entities/job.entity';
import { CustomerInfoType } from '../../../entities';
import { IS_DEV, defaultValue } from '../..';
import { generatePdf } from '../PDF-config-general/generate-page';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import {
  getSectionColumnConfig,
  getBoxPosition,
  drawDetailTableByListOfData,
  drawTextLineByListOfData,
  drawRowByListOfData,
} from '../PDF-config-general/generate-component';
import { DateTime } from 'luxon';

export default async function generateContract({
  job,
  branchTitle,
  companyLogo,
  customerPhoto,
  type,
}: {
  job: JobEntity;
  branchTitle: string;
  companyLogo: Uint8Array;
  customerPhoto?: Uint8Array;
  type: 'CONTRACT' | 'TRANSACTION';
}) {
  try {
    if (
      type === 'CONTRACT' &&
      (typeof job.contract?.customerInfo === 'string' ||
        job.contract?.customerInfo === undefined)
    ) {
      throw new Error('customerInfo is invalid');
    }

    let customerInfo = job.contract?.customerInfo;

    // Set path depend on env
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';

    // --- generate new pdf with ww template
    let title = '';
    switch (type) {
      case 'CONTRACT':
        title = 'เอกสารสรุปผลการตรวจสภาพสินค้า';
        break;
      case 'TRANSACTION':
        title = 'ใบแสดงรายละเอียดสินค้า';
        break;
      default:
        return '';
    }

    const {
      pdfDoc,
      action: { addText, addRectangle },
      font,
      colorDefault,
      page1: page,
    } = await generatePdf({
      currentPath,
      companyLogo,
      title,
      pdfPaperConfig: PDF_PAPER_CONTAINER_WW,
    });

    // --- prompt image
    const IMAGE_IDCARD_BG = await fs.readFile(
      `${currentPath}/assets/img/id-card.png`,
    );
    const IMAGE_IDCARD_CERTIFIED = await fs.readFile(
      `${currentPath}/assets/img/certified-ww.png`,
    );

    const transactionIdValue = job.jobId;
    const barcodeTransactionIdImg = await generateBarcode(transactionIdValue);

    // embed
    const imageIDCardBg = await pdfDoc.embedPng(IMAGE_IDCARD_BG);
    const imageIDCardCertified = await pdfDoc.embedPng(IMAGE_IDCARD_CERTIFIED);
    const barcodeTransaction = await pdfDoc.embedPng(barcodeTransactionIdImg);

    // --- data text label and value
    // prompt transaction
    const date = job?.contract?.createdAt || new Date();
    const dateWithFormat = DateTime.fromJSDate(date, {
      zone: 'Asia/Bangkok',
    })
      .reconfigure({ outputCalendar: 'buddhist' })
      .toFormat('dd/MM/yyyy');

    // prompt user inf
    const { firstName, lastName } = customerInfo?.thaiName || {};
    const fullNameTh = `${firstName} ${lastName}`;
    const idCardCustomer =
      customerInfo?.identificationNumber.split('-').join('') || '';
    const idCard = defaultValue(
      idCardCustomer.replace(
        /(\d{1})(\d{4})(\d{5})(\d{2})(\d{1})/,
        '$1 $2 $3 $4 $5',
      ),
    );

    // id card
    const { houseNumber, soi, road, moo, subdistrict, district, province } =
      customerInfo?.address || { subdistrict: {}, district: {}, province: {} };
    const address1 = [houseNumber, moo, soi, road].filter(Boolean);
    const resultAddress1 = address1.length > 0 ? address1.join(' ') : '';
    const address2 = [subdistrict.name, district.name, province.name].filter(
      Boolean,
    );
    const resultAddress2 = address2.length > 0 ? address2.join(' ') : '';
    const address = `ที่อยู่ ${resultAddress1}\n${resultAddress2}`;

    const idCardInfo = {
      id: idCard,
      fullNameTh: defaultValue(`${customerInfo?.thaiName.title} ${fullNameTh}`),
      nameEn: defaultValue(
        `${customerInfo?.engName.title} ${customerInfo?.engName.firstName}`,
      ),
      lastnameEn: defaultValue(customerInfo?.engName.lastName),
      dateOfBirthTh: defaultValue(
        formatDateIdCardTh(customerInfo?.birthDate || ''),
      ),
      dateOfBirthEn: defaultValue(
        formatDateIdCardEn(customerInfo?.birthDate || ''),
      ),
      address,
      dateIssueTh: formatDateIdCardTh(customerInfo?.issueDate || ''),
      dateIssueEn: formatDateIdCardEn(customerInfo?.issueDate || ''),
      dateExpireTh: formatDateIdCardTh(customerInfo?.expireDate || ''),
      dateExpireEn: formatDateIdCardEn(customerInfo?.expireDate || ''),
    };

    // terms
    const terms: { label: string; line?: number }[] = [
      {
        label:
          '1. ลูกค้ายืนยันความเป็นเจ้าของ และ ข้อมูลที่ให้ รวมถึงสภาพสินค้าที่แจ้งเป็นความจริงทุกประการ',
      },
      {
        label:
          '2. ลูกค้ายืนยันว่าได้รับ e-Gift Voucher เรียบร้อยแล้ว ผ่านช่องทางอีเมล (Email) ตามที่ลูกค้าได้แจ้งไว้',
      },
      {
        label: '3. e-Gift Voucher สามารถใช้ซื้อสินค้าตามที่ W&W กำหนดเท่านั้น',
      },
      {
        label:
          '4. e-Gift Voucher นี้ สามารถใช้ร่วมกับการชำระค่าสินค้า, อุปกรณ์เสริมต่างๆ ด้วยเงินสด และ/หรือบัตรเครดิตได้ ณ สาขาที่ร่วมรายการ',
      },
      {
        label:
          '5. e-Gift Voucher ไม่สามารถทอน หรือแลกเปลี่ยนเป็นเงินสด, คืน, โอน หรือเติมเงินไปยังบัญชี ทรูมันนี่ของตนเอง และผู้อื่นได้',
      },
      { label: '6. ไม่สามารถยกเลิกการทำรายการได้' },
      {
        label:
          '7. W&W ขอสงวนสิทธิ์ในการรับ คืน ยกเลิก รวมถึงไม่ชดเชยความเสียหายกรณี e-Gift Voucher สูญหาย',
      },
    ];

    // data by section
    const dataBySection = {
      transactionInfo: {
        columnDetails: [
          {
            label: 'วันที่',
            value: dateWithFormat,
          },
          { label: 'สาขาที่รับฝาก', value: defaultValue(branchTitle) },
          {
            label: 'พนักงานที่รับฝาก',
            value: defaultValue(job.shopUserName),
          },
          { label: 'Transaction ID' },
        ],
      },
      userInfo: {
        sectionHeader: 'ข้อมูลเจ้าของสินค้า(ลูกค้า)',
        rowDetails: [
          {
            label: 'ชื่อ-นามสกุล',
            value: defaultValue(fullNameTh),
            labelWidth: 41,
          },
          {
            label: 'เลขบัตรประชาชน',
            value: defaultValue(idCardCustomer),
            labelWidth: 57,
          },
          {
            label: 'หมายเลขโทรศัพท์',
            value: defaultValue(customerInfo?.mobileNumber),
            labelWidth: 58,
          },
          {
            label: 'อีเมล',
            value: defaultValue(customerInfo?.email),
            labelWidth: 17,
          },
        ],
      },
      purchaseInfo: {
        sectionHeader: 'ข้อมูลการประเมิน',
        columnDetails: [
          {
            label: 'ผลการประเมิน',
            value: getMobileGrade(
              job.suggestedPrice || 0,
              job.modelTemplate.modelMasterGrades,
            ),
          },
          {
            label: 'มูลค่าการรับฝาก',
            value: defaultValue(formatPrice(job.suggestedPrice || 0), '-'),
          },
        ],
        data: {
          grade: {
            label: 'ผลการประเมิน',
            value: getMobileGrade(
              job.suggestedPrice || 0,
              job.modelTemplate.modelMasterGrades,
            ),
            labelWidth: 48,
            valueWidth: 10,
          },
          price: {
            label: 'มูลค่าการรับฝาก',
            value: defaultValue(formatPrice(job.suggestedPrice || 0), '-'),
            labelWidth: 56,
          },
        },
      },
      modelInfo: {
        sectionHeader: 'รายละเอียดสินค้า',
        columnDetails: [
          { label: 'ยี่ห้อ', value: job.modelIdentifiers.brand },
          { label: 'รุ่น', value: job.modelIdentifiers.model },
          { label: 'ความจุ', value: job.modelIdentifiers.rom },
          {
            label: 'สี',
            value: job.color?.nameTh || '-',
          },
          { label: 'IMEI / Serial', value: job.deviceKey },
          { label: 'IMEI 2 / Serial 2', value: defaultValue(job.deviceKey2) },
          {
            label: 'โมเดลประเทศ',
            value: findCheckListValueChoice(job, 'country_of_purchase').text,
          },
        ],
      },
      evaluateResult: {
        sectionHeader: 'สภาพสินค้า / อุปกรณ์เสริม',
        columnDetails: [
          {
            label: 'สภาพตัวเครื่อง',
            value: findCheckListValueChoice(job, 'device_condition').text || '',
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'device_condition').value,
            ),
          },
          {
            label: 'การแสดงภาพหน้าจอ',
            value: findCheckListValueChoice(job, 'screen_display').text || '',
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'screen_display').value,
            ),
          },
          {
            label: 'อุปกรณ์เสริม (กล่อง, หูฟัง, สายชาร์จ, หัวชาร์จ)',
            value:
              findCheckListValueChoice(job, 'additional_accessories').text ||
              '',
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'additional_accessories').value,
            ),
            line: 2,
          },
          {
            label: 'สามารถออกจาก iCloud หรือ Google Account',
            value: findCheckListValueChoice(job, 'icloud_or_google_account')
              .text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'icloud_or_google_account').value,
            ),
            line: 2,
          },
          {
            label: 'สุขภาพแบตเตอรี่',
            value: findCheckListValueChoice(job, 'battery_health').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'battery_health').value,
            ),
          },
        ],
      },
      functionalResult: {
        sectionHeader: 'ฟังก์ชั่นการใช้งาน',
        columnDetails: [
          {
            label: 'Bluetooth',
            value: findCheckListValueChoice(job, 'bluetooth').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'bluetooth').value,
            ),
          },
          {
            label: 'Wifi',
            value: findCheckListValueChoice(job, 'wifi').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'wifi').value,
            ),
          },
          {
            label: 'กล้องหน้า',
            value: findCheckListValueChoice(job, 'front_camera').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'front_camera').value,
            ),
          },
          {
            label: 'กล้องหลัง',
            value: findCheckListValueChoice(job, 'rear_camera').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'rear_camera').value,
            ),
          },
          {
            label: 'ไมโครโฟน',
            value: findCheckListValueChoice(job, 'microphone').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'microphone').value,
            ),
          },
          {
            label: 'Face ID/Touch ID',
            value: findCheckListValueChoice(job, 'biometric').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'biometric').value,
            ),
          },
          {
            label: 'การชาร์จไฟ',
            value: findCheckListValueChoice(job, 'power_charging').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'power_charging').value,
            ),
          },
          {
            label: 'เซนเซอร์ระยะห่าง',
            value: findCheckListValueChoice(job, 'proximity_sensor').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'proximity_sensor').value,
            ),
          },
          {
            label: 'การสั่น',
            value: findCheckListValueChoice(job, 'vibration').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'vibration').value,
            ),
          },
          {
            label: 'ลำโพง',
            value: findCheckListValueChoice(job, 'speaker').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'speaker').value,
            ),
          },
          {
            label: 'ลำโพงแนบหู',
            value: findCheckListValueChoice(job, 'earpiece').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'earpiece').value,
            ),
          },
          {
            label: 'ปุ่มเพิ่ม - ลดเสียง',
            value: findCheckListValueChoice(job, 'volume_button').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'volume_button').value,
            ),
          },
          {
            label: 'การสัมผัสหน้าจอ',
            value: findCheckListValueChoice(job, 'touch_screen').text,
            isItalic: isItalicCheckListResult(
              findCheckListValueChoice(job, 'touch_screen').value,
            ),
          },
        ],
      },
      signSection: {
        data: {
          signature: {
            label: 'ลูกค้า/ผู้ฝากสินค้า',
            value: fullNameTh,
            date: `วันที่ ${dateWithFormat}`,
          },
        },
      },
    };

    // --- prompt calculate
    const {
      WIDTH,
      HEIGHT,
      PAPER_MARGIN,
      BLOCK,
      FONT: { LABEL, TEXT, HIGHLIGHT, SIGNATURE },
      SIGN,
    } = PDF_PAPER_CONTAINER_WW;
    let accYPositonBox = 44;
    const uiProp = {
      addText,
      font,
      colorDefault,
      PDF_PAPER_CONTAINER: PDF_PAPER_CONTAINER_WW,
    };

    // --- util box and section
    const widthBox = WIDTH - PAPER_MARGIN.LEFT - PAPER_MARGIN.RIGHT;

    // --- section to draw
    const drawTransactionInfo = async () => {
      const columnCount = 4;
      const BOX_HEIGHT = 71;
      const BARCODE_HEIGHT = 33;
      const CHAR_WIDTH = 4.6;
      // calculate
      const { boxPosition, headerPositionY: labelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      const positionBarcodeTextY = boxPosition.y + BLOCK.PADDING.BOTTOM;
      const positionBarcodeY = positionBarcodeTextY + TEXT.SIZE;
      const positionBarcodeTextX =
        columnPositionX[3] +
        (columnWidth - CHAR_WIDTH * transactionIdValue.length) / 2;

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      drawDetailTableByListOfData({
        listOfData: dataBySection.transactionInfo.columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: labelPositionY,
        uiProp,
      });

      // draw barcode
      page.drawImage(barcodeTransaction, {
        x: columnPositionX[3],
        y: positionBarcodeY,
        width: columnWidth,
        height: BARCODE_HEIGHT,
      });

      addText(
        transactionIdValue,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        positionBarcodeTextX,
        positionBarcodeTextY,
      );

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawUserData = async () => {
      const columnCount = 3;
      const BOX_HEIGHT = 120;
      const ID_CARD_SIZE = { WIDTH: 166, HEIGHT: 104 };
      const BAHT_WIDTH = 28;
      const NUM_WIDTH = 8.5;
      const COMMA_WIDTH = 3;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      const oneForthYHeight =
        (BOX_HEIGHT - BLOCK.PADDING.TOP - BLOCK.PADDING.BOTTOM) / 4;
      const numOnly = dataBySection.purchaseInfo.data.price.value
        .split(',')
        .join('')
        .slice(0, -4);
      const commaCount = Math.floor(numOnly.length / 3);
      const priceWidth =
        BAHT_WIDTH + numOnly.length * NUM_WIDTH + commaCount * COMMA_WIDTH;
      const positionPrice = {
        grade: {
          label: {
            x:
              columnPositionX[2] +
              (columnWidth - dataBySection.purchaseInfo.data.grade.labelWidth) /
                2,
            y:
              boxPosition.y +
              BLOCK.PADDING.BOTTOM +
              3 * oneForthYHeight +
              (oneForthYHeight - LABEL.SIZE) / 2,
          },
          value: {
            x:
              columnPositionX[2] +
              (columnWidth - dataBySection.purchaseInfo.data.grade.valueWidth) /
                2,
            y:
              boxPosition.y +
              BLOCK.PADDING.BOTTOM +
              3 * oneForthYHeight -
              HIGHLIGHT.SIZE,
          },
        },
        price: {
          label: {
            x:
              columnPositionX[2] +
              (columnWidth - dataBySection.purchaseInfo.data.price.labelWidth) /
                2,
            y:
              boxPosition.y +
              BLOCK.PADDING.BOTTOM +
              oneForthYHeight +
              (oneForthYHeight - LABEL.SIZE) / 2,
          },
          value: {
            x: columnPositionX[2] + (columnWidth - priceWidth) / 2,
            y:
              boxPosition.y +
              BLOCK.PADDING.BOTTOM +
              oneForthYHeight -
              HIGHLIGHT.SIZE,
          },
        },
      };

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      // draw image section
      drawCustomerImage(
        {
          x: columnPositionX[0],
          y: boxPosition.y + BLOCK.PADDING.BOTTOM,
        },
        { width: ID_CARD_SIZE.WIDTH, height: ID_CARD_SIZE.HEIGHT },
      );

      // draw info section
      addText(
        dataBySection.userInfo.sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[1],
        headerPositionY,
      );

      drawRowByListOfData({
        listOfData: dataBySection.userInfo.rowDetails,
        xPosition: columnPositionX[1],
        firstRowYPosition: R1LabelPositionY,
        uiProp: uiProp,
      });

      // draw price section
      addText(
        dataBySection.purchaseInfo.data.grade.label,
        font.light,
        LABEL.SIZE,
        LABEL.COLOR,
        positionPrice.grade.label.x,
        positionPrice.grade.label.y,
      );

      addText(
        dataBySection.purchaseInfo.data.grade.value,
        font.medium,
        HIGHLIGHT.SIZE,
        HIGHLIGHT.COLOR,
        positionPrice.grade.value.x,
        positionPrice.grade.value.y,
      );

      addText(
        dataBySection.purchaseInfo.data.price.label,
        font.light,
        LABEL.SIZE,
        LABEL.COLOR,
        positionPrice.price.label.x,
        positionPrice.price.label.y,
      );

      addText(
        dataBySection.purchaseInfo.data.price.value,
        font.medium,
        HIGHLIGHT.SIZE,
        HIGHLIGHT.COLOR,
        positionPrice.price.value.x,
        positionPrice.price.value.y,
      );

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawPurchaseInfo = async () => {
      const { sectionHeader, columnDetails } = dataBySection.purchaseInfo;
      const columnCount = 4;
      const BOX_HEIGHT = 59;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawModelInfo = async () => {
      const { sectionHeader, columnDetails } = dataBySection.modelInfo;
      const columnCount = 4;
      const BOX_HEIGHT = 89;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawEvaluateResult = async () => {
      const { sectionHeader, columnDetails } = dataBySection.evaluateResult;
      const columnCount = 4;
      const BOX_HEIGHT = 102;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawFunctionalResult = async () => {
      const { sectionHeader, columnDetails } = dataBySection.functionalResult;
      const columnCount = 4;
      const BOX_HEIGHT = 156;
      // calculate
      const { boxPosition, R1LabelPositionY, headerPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT;
    };

    const drawTermAndCondition = async () => {
      // calculate
      const firstLineY = HEIGHT - (accYPositonBox + TEXT.HEIGHT);

      // draw
      const boxHeight = drawTextLineByListOfData({
        textLines: terms,
        positionYFirstLine: firstLineY,
        addText,
        font,
        colorDefault,
        PDF_PAPER_CONTAINER: PDF_PAPER_CONTAINER_WW,
      });

      // post draw
      accYPositonBox += boxHeight;
    };

    const drawSignSection = async () => {
      const SIGN_LABEL_WIDTH = 64;
      const { signature } = dataBySection.signSection.data;
      // calculate
      const signatureLabelPositionX =
        SIGN.POSITION.X + (SIGN.WIDTH - SIGN_LABEL_WIDTH) / 2;

      // draw
      addText(
        signature.label,
        font.medium,
        SIGNATURE.SIZE,
        colorDefault,
        signatureLabelPositionX,
        SIGN.POSITION.Y + SIGN.HEIGHT + BLOCK.ROW_GAP,
      );

      page.drawLine({
        start: { x: SIGN.POSITION.X, y: SIGN.POSITION.Y },
        end: { x: SIGN.POSITION.X + SIGN.WIDTH, y: SIGN.POSITION.Y },
        thickness: 1,
        color: rgb(0, 0, 0),
      });

      addText(
        signature.value,
        font.medium,
        SIGNATURE.SIZE,
        colorDefault,
        signatureLabelPositionX,
        SIGN.POSITION.Y - SIGNATURE.HEIGHT,
      );

      addText(
        signature.date,
        font.medium,
        SIGNATURE.SIZE,
        colorDefault,
        signatureLabelPositionX,
        SIGN.POSITION.Y - SIGNATURE.HEIGHT - SIGNATURE.HEIGHT,
      );
    };

    // --- image id card
    const drawCustomerImage = async (
      position: { x: number; y: number },
      size: { width: number; height: number },
    ) => {
      switch (customerInfo?.type) {
        case CustomerInfoType.DIP_CHIP:
          await drawIdCard({ position, size });
          break;
        case CustomerInfoType.IDENTITY_VERIFICATION:
          await drawSecondAuthInfo({ position, size });
          break;
        default:
          break;
      }
      await drawCertified({ position, size });
    };

    const drawIdCard = async ({
      position,
      size,
    }: {
      position: { x: number; y: number };
      size: { width: number; height: number };
    }) => {
      if (customerPhoto) {
        const color = {
          black: rgb(0, 0, 0),
          blue: rgb(14 / 255, 100 / 255, 202 / 255),
        };
        // x,y relate to position of bg image
        const CONFIG_CUSTOMER_PHOTO = {
          X: 128,
          Y: 10.96,
          WIDTH: 33,
          HEIGHT: 39,
        };

        const testInIdCardList = [
          {
            value: idCard,
            config: {
              x: 72,
              y: 84.56,
              size: 5,
              color: color.black,
            },
          },
          {
            value: idCardInfo.fullNameTh,
            config: {
              x: 54,
              y: 75.96,
              size: 5,
              color: color.black,
            },
          },
          {
            value: idCardInfo.nameEn,
            config: {
              x: 64,
              y: 68.56,
              size: 5,
              color: color.blue,
            },
          },
          {
            value: idCardInfo.lastnameEn,
            config: {
              x: 68,
              y: 60.56,
              size: 5,
              color: color.blue,
            },
          },
          {
            value: idCardInfo.dateOfBirthTh,
            config: {
              x: 65,
              y: 51.56,
              size: 5,
              color: color.black,
            },
          },
          {
            value: idCardInfo.dateOfBirthEn,
            config: {
              x: 72,
              y: 43.56,
              size: 5,
              color: color.blue,
            },
          },
          {
            value: idCardInfo.address,
            config: {
              x: 16,
              y: 32.96,
              size: 5,
              color: color.black,
            },
          },
          {
            value: idCardInfo.dateIssueTh,
            config: {
              x: 16,
              y: 19.96,
              size: 3,
              color: color.black,
            },
          },
          {
            value: idCardInfo.dateIssueEn,
            config: {
              x: 16,
              y: 9.96,
              size: 3,
              color: color.blue,
            },
          },
          {
            value: idCardInfo.dateExpireTh,
            config: {
              x: 97,
              y: 19.96,
              size: 3,
              color: color.black,
            },
          },
          {
            value: idCardInfo.dateExpireEn,
            config: {
              x: 97,
              y: 9.96,
              size: 3,
              color: color.blue,
            },
          },
        ];

        const customerPhotoEmbed = await pdfDoc.embedJpg(customerPhoto);

        // draw
        page.drawImage(imageIDCardBg, {
          x: position.x,
          y: position.y,
          width: size.width,
          height: size.height,
        });

        page.drawImage(customerPhotoEmbed, {
          x: position.x + CONFIG_CUSTOMER_PHOTO.X,
          y: position.y + CONFIG_CUSTOMER_PHOTO.Y,
          width: CONFIG_CUSTOMER_PHOTO.WIDTH,
          height: CONFIG_CUSTOMER_PHOTO.HEIGHT,
        });

        testInIdCardList.forEach((item) => {
          addText(
            item.value,
            font.medium,
            item.config.size,
            item.config.color,
            position.x + item.config.x,
            position.y + item.config.y,
          );
        });
      }
    };

    const drawSecondAuthInfo = async ({
      position,
      size,
    }: {
      position: { x: number; y: number };
      size: { width: number; height: number };
    }) => {
      if (customerPhoto) {
        const customerPhotoEmbed = await pdfDoc.embedPng(customerPhoto);

        page.drawImage(customerPhotoEmbed, {
          x: position.x,
          y: position.y,
          width: size.width,
          height: size.height,
        });
      }
    };

    const drawCertified = async ({
      position,
      size,
    }: {
      position: { x: number; y: number };
      size: { width: number; height: number };
    }) => {
      page.drawImage(imageIDCardCertified, {
        x: position.x,
        y: position.y + 10,
        width: size.width,
        height: size.height - 20,
      });
    };

    // --- render
    await drawTransactionInfo();
    if (type === 'CONTRACT') {
      await drawUserData();
    } else if (type === 'TRANSACTION') {
      await drawPurchaseInfo();
    }
    await drawModelInfo();
    await drawEvaluateResult();
    await drawFunctionalResult();
    if (type === 'CONTRACT') {
      await drawTermAndCondition();
      await drawSignSection();
    }
    const savedPdf = await pdfDoc.saveAsBase64({
      objectsPerTick: Infinity,
    });
    return savedPdf;
  } catch (error) {
    throw new Error('Failed to generate contract ' + error);
  }
}
