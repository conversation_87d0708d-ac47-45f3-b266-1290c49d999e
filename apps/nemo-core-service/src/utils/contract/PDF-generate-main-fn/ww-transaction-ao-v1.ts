import {
  createRectangleAdder,
  createTextAdder,
  generateBarcode,
} from '../PDF-config-general/helper';
import { JobEntity } from '../../../entities/job.entity';
import { AllocationOrderEntity, AllocationOrderType } from '../../../entities';
import { IS_DEV } from '../..';
import { generatePdf } from '../PDF-config-general/generate-page';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import {
  getSectionColumnConfig,
  getBoxPosition,
  drawDetailTableByListOfData,
  drawTable,
  ITableConfig,
} from '../PDF-config-general/generate-component';
import { DateTime } from 'luxon';
import { rgb } from 'pdf-lib';

const MAX_TABLE_DATA_PER_PAGE = 22;
const POSITION_Y_START = 44;

// --- general util
const dateToDateString = (date: Date, format: string) =>
  DateTime.fromJSDate(new Date(date), {
    zone: 'Asia/Bangkok',
  })
    .reconfigure({ outputCalendar: 'buddhist' })
    .toFormat(format);

const moneyFormatter = new Intl.NumberFormat('th-TH', {
  style: 'decimal',
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

// --- table util
const defaultTableAOConfig: ITableConfig[] = [
  { field: 'jobId', label: 'Transaction ID', width: 'auto' },
  { field: 'deviceKey', label: 'เลข IMEI', width: 'auto' },
  { field: 'brand', label: 'ยี่ห้อ', width: 80 },
  { field: 'model', label: 'รุ่น', width: 'auto' },
  { field: 'grade', label: 'เกรด', width: 30 },
];

const getTableAOConfig = (type: AllocationOrderType, widthTable: number) => {
  const config: ITableConfig[] = [...defaultTableAOConfig];
  if (type === AllocationOrderType.RETAIL) {
    config.push({
      field: 'retailPrice',
      label: 'ราคาขายปลีก',
      width: 'auto',
      align: 'RIGHT',
    });
  } else if (type === AllocationOrderType.WHOLESALE) {
    config.push({
      field: 'wholeSalePrice',
      label: 'ราคาขายส่ง',
      width: 'auto',
      align: 'RIGHT',
    });
  }
  let accFixedWidthPx = 0;
  let countAutoWidth = 0;
  config.forEach((item: any) => {
    const { width } = item;
    if (width === 'auto') {
      countAutoWidth += 1;
    } else {
      accFixedWidthPx += width;
    }
  });
  const autoWidth = (widthTable - accFixedWidthPx) / countAutoWidth;

  return config.map((item) => {
    const width: number = item.width === 'auto' ? autoWidth : item.width;
    return { ...item, width };
  });
};

// --- main function for generate
export default async function generateAoTransaction({
  allocationOrder,
  jobs,
  companyLogo,
}: {
  allocationOrder: AllocationOrderEntity;
  jobs: JobEntity[];
  companyLogo: Uint8Array;
}) {
  try {
    // Set path depend on env
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';

    // --- generate new pdf with ww template
    const title = 'ใบแสดงรายละเอียดสินค้า';

    let {
      pdfDoc,
      action: { addText, addRectangle },
      font,
      colorDefault,
      page1,
      addNewPage,
      drawHeader,
    } = await generatePdf({
      currentPath,
      companyLogo,
      title,
      pdfPaperConfig: PDF_PAPER_CONTAINER_WW,
    });
    // --- prompt image

    const AOIdValue = allocationOrder.allocationOrderId;
    const barcodeAOIdImg = await generateBarcode(AOIdValue);

    // embed
    const barcodeAO = await pdfDoc.embedPng(barcodeAOIdImg);

    // --- data text label and value
    const aoType = allocationOrder.allocationOrderType;
    let aoTypeValue = '';
    let aoDateAction = '';
    let deliveryDate = new Date();
    if (aoType === AllocationOrderType.RETAIL) {
      aoTypeValue = 'ขายปลีก';
      aoDateAction = 'วันที่นัดหมาย';
      deliveryDate = allocationOrder.appointmentDate as Date;
    } else if (aoType === AllocationOrderType.WHOLESALE) {
      aoTypeValue = 'ขายส่ง';
      aoDateAction = 'วันที่ส่งสินค้า';
      deliveryDate = allocationOrder.pickupDate as Date;
    }

    // table
    const jobDataForTable = jobs.map((job: JobEntity) => {
      const {
        jobId,
        deviceKey,
        modelIdentifiers: { model, brand },
        currentGrade,
        retailPrice,
        wholeSalePrice,
      } = job;
      return {
        jobId,
        deviceKey,
        brand,
        model,
        grade: currentGrade || 'D',
        retailPrice:
          retailPrice || retailPrice === 0
            ? moneyFormatter.format(retailPrice)
            : '-',
        wholeSalePrice:
          wholeSalePrice || wholeSalePrice === 0
            ? moneyFormatter.format(wholeSalePrice)
            : '-',
      };
    });

    const jobCount = jobDataForTable.length;
    const pageCount = Math.ceil(jobCount / MAX_TABLE_DATA_PER_PAGE);

    // data by section
    const dataBySection = {
      AOInfo: {
        columnDetails: [
          {
            label: 'วันที่',
            value: allocationOrder.createdAt
              ? dateToDateString(allocationOrder.createdAt, 'dd/MM/yyyy')
              : '-',
          },
          {
            label: 'พนักงานที่สร้างใบ AO',
            value: allocationOrder.createdUser?.name ?? 'ระบบสร้างอัตโนมัติ',
          },
          { label: 'Allocation Order' },
        ],
      },
      AWBDetail: {
        sectionHeader: 'รายละเอียด AO',
        columnDetails: [
          {
            label: 'เลข Air way bill',
            value: allocationOrder.awbNumber || '-',
          },
          {
            label: aoDateAction,
            value: deliveryDate
              ? dateToDateString(deliveryDate, 'dd/MM/yyyy')
              : '-',
          },
          { label: 'จำนวนเครื่อง', value: jobs.length.toString() },
          { label: 'ประเภท', value: aoTypeValue },
          {
            label: 'สาขาต้นทาง',
            value: `${allocationOrder.fromBranch?.branchId} ${allocationOrder.fromBranch?.title}`,
            span: 4,
          },
          {
            label: 'สาขาปลายทาง',
            value: `${allocationOrder.toBranch?.branchId} ${allocationOrder.toBranch?.title}`, //    toBranch,
            span: 4,
          },
        ],
      },
    };

    // --- prompt calculate
    const {
      WIDTH,
      PAPER_MARGIN,
      BLOCK,
      FONT: { LABEL, TEXT },
    } = PDF_PAPER_CONTAINER_WW;
    let accYPositonBox = POSITION_Y_START;

    // --- util box and section
    const widthBox = WIDTH - PAPER_MARGIN.LEFT - PAPER_MARGIN.RIGHT;
    const widthBoxContent = widthBox - BLOCK.PADDING.LEFT - BLOCK.PADDING.RIGHT;

    const tableAOConfig = getTableAOConfig(aoType, widthBoxContent); // allocationOrderType,

    // --- section to draw
    const drawAOInfo = async (page, uiProp) => {
      const columnCount = 3;
      const BOX_HEIGHT = 71;
      const BARCODE_HEIGHT = 33;
      // calculate
      const { boxPosition, headerPositionY: labelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      const positionBarcodeTextY = boxPosition.y + BLOCK.PADDING.BOTTOM;
      const positionBarcodeY = positionBarcodeTextY + TEXT.SIZE;
      const widthText = font.medium.widthOfTextAtSize(AOIdValue, TEXT.SIZE);
      const positionBarcodeTextX =
        columnPositionX[2] + (columnWidth - widthText) / 2;

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      drawDetailTableByListOfData({
        listOfData: dataBySection.AOInfo.columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: labelPositionY,
        uiProp,
      });

      // draw barcode
      page.drawImage(barcodeAO, {
        x: columnPositionX[2],
        y: positionBarcodeY,
        width: columnWidth,
        height: BARCODE_HEIGHT,
      });

      addText(
        AOIdValue,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        positionBarcodeTextX,
        positionBarcodeTextY,
      );

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawAWBDetail = async (uiProp) => {
      const { sectionHeader, columnDetails } = dataBySection.AWBDetail;
      const columnCount = 4;
      const BOX_HEIGHT = 121;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawJobTable = async (
      data,
      pageLabel: { current: number; total: number },
      uiProp,
    ) => {
      const sectionHeader = 'รายการสินค้า';
      const columnCount = 1;
      const TABLE_ROW_HEIGHT = 24;
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      // calculate
      const boxHeight =
        (1 + data.length) * TABLE_ROW_HEIGHT +
        BLOCK.PADDING.TOP +
        BLOCK.PADDING.BOTTOM +
        2 * BLOCK.ROW_GAP +
        LABEL.SIZE;
      const { boxPosition, headerPositionY } = getBoxPosition(
        boxHeight,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const tableHeaderPositionY =
        headerPositionY - TABLE_ROW_HEIGHT - 2 * BLOCK.ROW_GAP;
      // draw util
      const drawHorizontalLine = (positionY: number) => {
        page.drawLine({
          start: { x: columnPositionX[0], y: positionY },
          end: { x: columnPositionX[0] + columnWidth, y: positionY },
          thickness: 1,
          color: rgb(215 / 255, 215 / 255, 215 / 255),
        });
      };

      // draw
      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      if (pageLabel.total > 1) {
        const paginate = `หน้า ${pageLabel.current}/${pageLabel.total}`;
        const widthText = font.medium.widthOfTextAtSize(paginate, LABEL.SIZE);
        const positionX =
          WIDTH - PAPER_MARGIN.RIGHT - BLOCK.PADDING.RIGHT - widthText;
        addText(
          paginate,
          font.medium,
          TEXT.SIZE,
          colorDefault,
          positionX,
          headerPositionY,
        );
      }

      drawTable({
        tableConfig: tableAOConfig,
        data,
        tableWidth: columnWidth,
        rowHeightDefault: TABLE_ROW_HEIGHT,
        positionStart: { x: columnPositionX[0], y: tableHeaderPositionY },
        uiProp,
        drawHorizontalLine,
      });

      addRectangle(boxPosition.x, boxPosition.y, widthBox, boxHeight);

      // post draw --- new page
      accYPositonBox = POSITION_Y_START;
    };

    // --- render
    let page = page1;

    // loop page render
    for (let i = 0; i < pageCount; i++) {
      // add new page setup
      if (i !== 0) {
        page = addNewPage();
        drawHeader({ page, title });
      }

      // declare dynamic util each page
      addRectangle = createRectangleAdder(page);
      addText = createTextAdder(page);
      const uiProp = {
        addText,
        font,
        colorDefault,
        PDF_PAPER_CONTAINER: PDF_PAPER_CONTAINER_WW,
      };
      const uiPropTable = {
        ...uiProp,
        addRectangle,
      };

      // draw each section
      await drawAOInfo(page, uiProp);
      await drawAWBDetail(uiProp);
      await drawJobTable(
        jobDataForTable.slice(
          i * MAX_TABLE_DATA_PER_PAGE,
          (i + 1) * MAX_TABLE_DATA_PER_PAGE,
        ),
        { current: i + 1, total: pageCount },
        uiPropTable,
      );
    }

    const savedPdf = await pdfDoc.saveAsBase64({
      objectsPerTick: Infinity,
    });
    return savedPdf;
  } catch (error) {
    throw new Error('Failed to generate transaction inspection' + error);
  }
}
