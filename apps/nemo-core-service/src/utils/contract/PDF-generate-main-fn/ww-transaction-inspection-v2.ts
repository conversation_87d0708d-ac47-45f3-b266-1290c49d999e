import { JobEntity, QCStatus } from '../../../entities/job.entity';
import { IS_DEV, defaultValue } from '../..';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import { generatePdf } from '../PDF-config-general/generate-page';
import {
  getSectionColumnConfig,
  getBoxPosition,
  drawDetailTableByListOfData,
} from '../PDF-config-general/generate-component';
import { generateBarcode } from '../PDF-config-general/helper';
import { DateTime } from 'luxon';

const dateToDateString = (date: Date, format: string) =>
  DateTime.fromJSDate(new Date(date), {
    zone: 'Asia/Bangkok',
  })
    .reconfigure({ outputCalendar: 'buddhist' })
    .toFormat(format);

export default async function generateInspectionTransaction({
  job,
  companyLogo,
}: {
  job: JobEntity;
  companyLogo: Uint8Array;
}) {
  try {
    // Set path depend on env
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';

    // --- generate new pdf with ww template
    let title = 'ใบแสดงรายละเอียดสินค้า';

    const {
      pdfDoc,
      action: { addText, addRectangle },
      font,
      colorDefault,
      page1: page,
    } = await generatePdf({
      currentPath,
      companyLogo,
      title,
      pdfPaperConfig: PDF_PAPER_CONTAINER_WW,
    });

    // --- prompt image

    const transactionIdValue = job.jobId;
    const imeiValue = job.deviceKey ?? '';
    const barcodeTransactionIdImg = await generateBarcode(transactionIdValue);
    const barcodeImeiIdImg = await generateBarcode(imeiValue);

    // embed
    const barcodeTransaction = await pdfDoc.embedPng(barcodeTransactionIdImg);
    const barcodeImei = await pdfDoc.embedPng(barcodeImeiIdImg);

    // --- data text label and value
    // prompt transaction
    const { repairListValue = [], inspectListValue = [] } = job;
    const repairData = repairListValue[repairListValue.length - 1];
    const inspectData = inspectListValue[inspectListValue.length - 1];

    // data by section
    const dataBySection = {
      transactionInfo: {
        columnDetails: [
          {
            label: 'วันที่',
            value: dateToDateString(new Date(), 'dd/MM/yyyy'),
          },
          { label: '' },
          { label: 'Transaction ID' },
        ],
      },
      modelInfo: {
        sectionHeader: 'รายละเอียดสินค้า',
        columnDetails: [
          { label: 'ยี่ห้อ', value: job.modelIdentifiers.brand },
          { label: 'รุ่น', value: job.modelIdentifiers.model },
          { label: 'ความจุ', value: job.modelIdentifiers.rom },
          { label: 'IMEI 2 / Serial 2', value: defaultValue(job.deviceKey2) },
        ],
      },
      lastRepair: {
        sectionHeader:
          repairData.type !== QCStatus.SCRAP
            ? 'ข้อมูลการซ่อม / Refurbish ล่าสุด'
            : 'ข้อมูลการขายซาก',
        columnDetails: [
          {
            label:
              repairData.type !== QCStatus.SCRAP
                ? 'กรอกรายละเอียดโดย'
                : 'ส่งขายซากโดย',
            value: repairData.by.name,
            lineValue: 2,
          },
          {
            label:
              repairData.type !== QCStatus.SCRAP
                ? 'วันที่รับงาน'
                : 'วันที่รับงานซ่อม',
            value: repairData.assignedAt
              ? dateToDateString(repairData.assignedAt, 'dd/MM/yyyy HH:mm')
              : null,
          },
          {
            label:
              repairData.type !== QCStatus.SCRAP
                ? 'วันที่ดำเนินการสำเร็จ'
                : 'วันที่ส่งขายซาก',
            value: repairData.at
              ? dateToDateString(repairData.at, 'dd/MM/yyyy HH:mm')
              : null,
          },
          { label: 'เกรด', value: repairData.grade || 'D' }, //NOSONAR
          {
            label: 'รายละเอียด',
            value: repairData.detail,
            span: 4,
            fixColIndex: true,
          },
        ],
      },
      lastInspection: {
        sectionHeader: 'ข้อมูลการ Inspection ล่าสุด',
        columnDetails: [
          {
            label: 'ตรวจสินค้าโดย',
            value:
              inspectData.by.name === 'system' && inspectData.isPassed
                ? 'ระบบตรวจสอบอัตโนมัติ'
                : inspectData.by.name,
            lineValue: 2,
          },
          {
            label: 'วันที่ตรวจสินค้า',
            value: inspectData.at
              ? dateToDateString(inspectData.at, 'dd/MM/yyyy HH:mm')
              : null,
          },

          {
            label: 'ผลตรวจสินค้า',
            value: inspectData.isPassed ? 'ผ่าน' : 'ไม่ผ่าน',
          },
        ],
      },

      additionalInfo: {
        columnDetails: [{ label: 'IMEI 1 / Serial 1' }],
      },
    };

    // --- prompt calculate
    const {
      WIDTH,
      PAPER_MARGIN,
      BLOCK,
      FONT: { TEXT },
    } = PDF_PAPER_CONTAINER_WW;
    const uiProp = {
      addText,
      font,
      colorDefault,
      PDF_PAPER_CONTAINER: PDF_PAPER_CONTAINER_WW,
    };
    let accYPositonBox = 44;

    // --- util box and section
    const widthBox = WIDTH - PAPER_MARGIN.LEFT - PAPER_MARGIN.RIGHT;

    // --- section to draw
    const drawTransactionInfo = async () => {
      const columnCount = 3;
      const BOX_HEIGHT = 71;
      const BARCODE_HEIGHT = 33;
      const CHAR_WIDTH = 4.6;
      // calculate
      const { boxPosition, headerPositionY: labelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      const positionBarcodeTextY = boxPosition.y + BLOCK.PADDING.BOTTOM;
      const positionBarcodeY = positionBarcodeTextY + TEXT.SIZE;
      const positionBarcodeTextX =
        columnPositionX[2] +
        (columnWidth - CHAR_WIDTH * transactionIdValue.length) / 2;

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      drawDetailTableByListOfData({
        listOfData: dataBySection.transactionInfo.columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: labelPositionY,
        uiProp,
      });

      // draw barcode
      page.drawImage(barcodeTransaction, {
        x: columnPositionX[2],
        y: positionBarcodeY,
        width: columnWidth,
        height: BARCODE_HEIGHT,
      });

      addText(
        transactionIdValue,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        positionBarcodeTextX,
        positionBarcodeTextY,
      );

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawModelInfo = async () => {
      const { sectionHeader, columnDetails } = dataBySection.modelInfo;
      const columnCount = 4;
      const BOX_HEIGHT = 89;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawLastRepair = async () => {
      const { sectionHeader, columnDetails } = dataBySection.lastRepair;
      const columnCount = 4;
      const BOX_HEIGHT = 118;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawLastInspection = async () => {
      const { sectionHeader, columnDetails } = dataBySection.lastInspection;
      const columnCount = 4;
      const BOX_HEIGHT = 72;
      // calculate
      const { boxPosition, headerPositionY, R1LabelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      addText(
        sectionHeader,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        columnPositionX[0],
        headerPositionY,
      );

      drawDetailTableByListOfData({
        listOfData: columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: R1LabelPositionY,
        uiProp,
      });

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    const drawAdditionalInfo = async () => {
      const columnCount = 3;
      const BOX_HEIGHT = 71;
      const BARCODE_HEIGHT = 33;
      const CHAR_WIDTH = 4.8;
      // calculate
      const { boxPosition, headerPositionY: labelPositionY } = getBoxPosition(
        BOX_HEIGHT,
        accYPositonBox,
        PDF_PAPER_CONTAINER_WW,
      );
      const { columnWidth, columnPositionX } = getSectionColumnConfig(
        columnCount,
        PDF_PAPER_CONTAINER_WW,
      );
      const positionBarcodeTextY = boxPosition.y + BLOCK.PADDING.BOTTOM;
      const positionBarcodeY = positionBarcodeTextY + TEXT.SIZE;
      const positionBarcodeTextX =
        columnPositionX[0] + (columnWidth - CHAR_WIDTH * imeiValue.length) / 2;

      // draw
      addRectangle(boxPosition.x, boxPosition.y, widthBox, BOX_HEIGHT);

      drawDetailTableByListOfData({
        listOfData: dataBySection.additionalInfo.columnDetails,
        columnCount,
        columnWidth,
        colPosition: columnPositionX,
        firstRowPosition: labelPositionY,
        uiProp,
      });

      // draw barcode
      page.drawImage(barcodeImei, {
        x: columnPositionX[0],
        y: positionBarcodeY,
        width: columnWidth,
        height: BARCODE_HEIGHT,
      });

      addText(
        imeiValue,
        font.medium,
        TEXT.SIZE,
        colorDefault,
        positionBarcodeTextX,
        positionBarcodeTextY,
      );

      // post draw
      accYPositonBox += BOX_HEIGHT + BLOCK.MARGIN.TOP;
    };

    // --- render
    await drawTransactionInfo();
    await drawModelInfo();
    await drawLastRepair();
    await drawLastInspection();
    await drawAdditionalInfo();

    const savedPdf = await pdfDoc.saveAsBase64({
      objectsPerTick: Infinity,
    });
    return savedPdf;
  } catch (error) {
    throw new Error('Failed to generate transaction inspection' + error);
  }
}
