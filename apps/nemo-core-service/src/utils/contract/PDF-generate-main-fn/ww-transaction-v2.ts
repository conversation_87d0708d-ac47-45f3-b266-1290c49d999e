import { JobEntity } from '../../../entities/job.entity';
import { IS_DEV } from '../..';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import { generatePdf } from '../PDF-config-general/generate-page';
import {
  getDataBySlug,
  getQuestionData,
  getModulenData,
} from '../PDF-config-data/ww-contract-v2';
import {
  calculateHeightModule,
  calculateHeightQuestionEach,
  drawComponentBySlugName,
  slugConfigContract,
} from '../PDF-config-slug/ww-contract-v2';
import {
  createRectangleAdder,
  createTextAdder,
  generateBarcode,
} from '../PDF-config-general/helper';
import { calculateComponentToEachPage } from '../PDF-config-general/generate-component';

export default async function generateTransactionV2({
  job,
  branchTitle,
  companyLogo,
}: {
  job: JobEntity;
  branchTitle: string;
  companyLogo: Uint8Array;
}) {
  try {
    // Set path depend on env
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';

    // --- generate new pdf with ww template
    const title = 'ใบแสดงรายละเอียดสินค้า';

    let {
      pdfDoc,
      action: { addText, addRectangle },
      font,
      colorDefault,
      page1,
      addNewPage,
      drawHeader,
    } = await generatePdf({
      currentPath,
      companyLogo,
      title,
      pdfPaperConfig: PDF_PAPER_CONTAINER_WW,
    });

    // --- prompt data
    const {
      dataBySlug: defaultDataBySlug,
      dateWithFormat,
      transactionIdValue,
    } = getDataBySlug({
      job,
      branchTitle,
    });

    const moduleSectionData = getModulenData(job).map(
      ({ title: label, answer: value, isItalic }) => ({
        label,
        value,
        isItalic,
      }),
    );
    const heightModule = calculateHeightModule(moduleSectionData.length, 28, 2);

    const questionSectionData: {
      order: number;
      label: string;
      results: string;
      line: number;
      height: number;
    }[] = [];
    const heightQuestion: number[] = [];
    getQuestionData(job).forEach(
      ({ title, answer, lineCount }, index: number) => {
        const heightItem = calculateHeightQuestionEach(lineCount, 14, 5);
        questionSectionData.push({
          order: index + 1,
          label: title,
          results: answer,
          line: lineCount,
          height: heightItem,
        });
        heightQuestion.push(heightItem);
      },
    );

    const dataBySection = {
      ...defaultDataBySlug,
      module: { ...defaultDataBySlug.module, columnDetails: moduleSectionData },
      question: {
        ...defaultDataBySlug.question,
        columnDetails: questionSectionData,
      },
    };

    // --- prompt image
    const barcodeTransactionIdImg = await generateBarcode(transactionIdValue);
    // embed
    const barcodeTransaction = await pdfDoc.embedPng(barcodeTransactionIdImg);

    // --- section to draw
    const components = slugConfigContract('transaction').components.map(
      (item) => {
        if (item.slug === 'question') {
          return { ...item, heightElements: heightQuestion };
        }
        if (item.slug === 'module') {
          return { ...item, heightElements: heightModule };
        }
        return { ...item };
      },
    );
    const slugConfig = { ...slugConfigContract('transaction'), components };
    const pagesComponent = calculateComponentToEachPage(slugConfig);

    // --- render
    let page = page1;

    for (let index = 0; index < pagesComponent.pages.length; index++) {
      const { components } = pagesComponent.pages[index];
      const { WIDTH, PAPER_MARGIN, BLOCK, PAGE_TITLE, FONT } =
        PDF_PAPER_CONTAINER_WW;
      let accYPositonBox =
        PAPER_MARGIN.TOP + PAGE_TITLE.HEIGHT + BLOCK.MARGIN.TOP;
      if (index !== 0) {
        page = addNewPage();
      }
      addRectangle = createRectangleAdder(page);
      addText = createTextAdder(page);
      const uiProp = {
        addText,
        font,
        colorDefault,
        PDF_PAPER_CONTAINER: PDF_PAPER_CONTAINER_WW,
      };

      const drawComponent = drawComponentBySlugName({
        pdfDoc,
        font,
        colorDefault,
        page,
        uiProp,
        accYPositonBox,
        addText,
        addRectangle,
        dataBySection,
        transactionIdValue,
        barcodeTransaction,
      });

      // draw page
      if (index !== 0) {
        drawHeader({ page, title });
      }
      const paginationText = `หน้า ${index + 1}/${pagesComponent.pages.length}`;
      const widthText = font.light.widthOfTextAtSize(
        paginationText,
        FONT.TEXT.SIZE,
      );

      addText(
        paginationText,
        font.light,
        FONT.TEXT.SIZE,
        colorDefault,
        (WIDTH - widthText) / 2,
        20,
      );

      for (const element of components) {
        const { slug, heightTotal } = element;
        await drawComponent[slug]({
          boxHeight: heightTotal,
          elementIndex: element.elementIndex,
        });
      }
    }

    const savedPdf = await pdfDoc.saveAsBase64({
      objectsPerTick: Infinity,
    });
    return savedPdf;
  } catch (error) {
    throw new Error('Failed to generate contract ' + error);
  }
}
