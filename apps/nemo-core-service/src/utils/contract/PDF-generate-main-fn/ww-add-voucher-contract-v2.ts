import { rgb } from 'pdf-lib';
import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import { stampImageToNewPdf } from '../PDF-config-general/generate-page';
import { generateBarcode, createTextAdder } from '../PDF-config-general/helper';
import { IS_DEV } from '../..';

export default async function addVoucherCode(
  pdfBase64: string,
  voucherCode: string,
) {
  try {
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';
    // --- const
    const {
      VOUCHER: {
        POSITION: { X, Y },
        WIDTH: WIDTH_VOUCHER,
        HEIGHT: HEIGHT_VOUCHER,
      },
      FONT: { TEXT },
    } = PDF_PAPER_CONTAINER_WW;
    const textDiffBottom = (TEXT.HEIGHT - TEXT.SIZE) / 2;
    // --- image
    const barcodeVoucherCodeImg = await generateBarcode(voucherCode);
    // --- generate new pdf doc and stamp
    const {
      pdfDoc,
      font,
      page: page1,
    } = await stampImageToNewPdf({
      pdfBase64,
      image: barcodeVoucherCodeImg,
      configImage: {
        x: X,
        y: Y,
        width: WIDTH_VOUCHER,
        height: HEIGHT_VOUCHER,
      },
      currentPath,
      stampOnPage: 'last',
    });
    const addText = createTextAdder(page1);
    // --- data prompt
    const text = [
      {
        text: 'โปรดเก็บ Barcode นี้ไว้เป็นความลับ',
        font: font.light,
        position: {
          x: X,
          y: Y + HEIGHT_VOUCHER + TEXT.HEIGHT + textDiffBottom,
        },
      },
      {
        text: 'Voucher Code',
        font: font.medium,
        position: { x: X, y: Y + HEIGHT_VOUCHER + textDiffBottom },
      },
      {
        text: voucherCode,
        font: font.medium,
        position: { x: X, y: Y - TEXT.HEIGHT + textDiffBottom },
      },
      {
        text: 'W&W ไม่รับผิดชอบในกรณีสูญหาย หรือแอบอ้างนำไปใช้',
        font: font.light,
        position: {
          x: X,
          y: Y - 2 * TEXT.HEIGHT + textDiffBottom,
        },
      },
    ];
    // --- draw
    text.forEach((item) => {
      const {
        text,
        font,
        position: { x, y },
      } = item;

      const width = font.widthOfTextAtSize(text, TEXT.SIZE);

      addText(
        text,
        font,
        TEXT.SIZE,
        rgb(0, 0, 0),
        x + (WIDTH_VOUCHER - width) / 2,
        y,
      );
    });
    // --- save pdf
    return await pdfDoc.save({
      objectsPerTick: Infinity,
    });
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to sign contract ' + error);
  }
}
