import { PDF_PAPER_CONTAINER_WW } from '../PDF-config-general/constant';
import { stampImageToNewPdf } from '../PDF-config-general/generate-page';
import { DateTime } from 'luxon';
import { IS_DEV } from '../..';

export default async function signDepositContract(
  pdfBase64: string,
  signature: { sign: string; firstName: string; lastName: string } | undefined,
) {
  try {
    const {
      sign: signatureBase64,
      firstName,
      lastName,
    } = signature || {
      sign: undefined,
      firstName: '',
      lastName: '',
    };

    // Set path depend on env
    const currentPath =
      process.env.STAGE === 'local' || IS_DEV
        ? './src/utils/contract'
        : './dist/src/apps/nemo-core-service/src/utils/contract';

    // --- generate new pdf with ww template
    const {
      SIGN,
      SIGN_DEPOSIT_CONTRACT: {
        POSITION: { X: DEPOSIT_SIGN_X, Y: DEPOSIT_SIGN_Y },
        WIDTH: DEPOSIT_SIGN_WIDTH,
        HEIGHT: DEPOSIT_SIGN_HEIGHT,
      },
      BLOCK,
      FONT: { SIGNATURE },
    } = PDF_PAPER_CONTAINER_WW;
    const {
      pdfDoc,
      font,
      colorDefault,
      action: { addText },
      page,
    } = await stampImageToNewPdf({
      pdfBase64,
      image: signatureBase64,
      configImage: {
        x: DEPOSIT_SIGN_X,
        y: DEPOSIT_SIGN_Y,
        width: DEPOSIT_SIGN_WIDTH,
        height: DEPOSIT_SIGN_HEIGHT,
      },
      currentPath,
    });

    // --- data text label and value
    const date = new Date();
    const dateWithFormat = DateTime.fromJSDate(date, {
      zone: 'Asia/Bangkok',
    })
      .reconfigure({ outputCalendar: 'buddhist' })
      .toFormat('dd/MM/yyyy');

    // calculate position
    const SIGN_LABEL_WIDTH = 64;
    const positionLabel =
      DEPOSIT_SIGN_X + (DEPOSIT_SIGN_WIDTH - SIGN_LABEL_WIDTH) / 2;

    // draw
    addText(
      'ลูกค้า/ผู้ฝากสินค้า',
      font.medium,
      SIGNATURE.SIZE,
      colorDefault,
      positionLabel,
      DEPOSIT_SIGN_Y + DEPOSIT_SIGN_HEIGHT + BLOCK.ROW_GAP,
    );

    page.drawLine({
      start: { x: DEPOSIT_SIGN_X, y: DEPOSIT_SIGN_Y },
      end: { x: DEPOSIT_SIGN_X + DEPOSIT_SIGN_WIDTH, y: DEPOSIT_SIGN_Y },
      thickness: 1,
      color: colorDefault,
    });

    addText(
      `${firstName} ${lastName}`,
      font.medium,
      SIGNATURE.SIZE,
      colorDefault,
      positionLabel,
      DEPOSIT_SIGN_Y - SIGNATURE.HEIGHT,
    );

    addText(
      `วันที่ ${dateWithFormat}`,
      font.medium,
      SIGNATURE.SIZE,
      colorDefault,
      positionLabel,
      DEPOSIT_SIGN_Y - SIGNATURE.HEIGHT - SIGNATURE.HEIGHT,
    );

    return await pdfDoc.saveAsBase64({
      objectsPerTick: Infinity,
    });
  } catch (error) {
    throw new Error('Failed to sign contract ' + error);
  }
}
