import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { WithUserContext } from '../../interfaces';
import {
  JobActivitiesEntity,
  JobEntity,
  JobStatus,
  JobActivityDetail,
  JobActivitiesType,
  JobShippingStatus,
  DeliveryOrderStatus,
  DeliveryOrderEntity,
  QCStatus,
  RepairHx,
  IRepairHxGrade,
  InspectHx,
  ImportedVoucherEntity,
  SystemConfigEntity,
  ExportActivitiesEntity,
  BranchEntity,
  UpdateCostHx,
  ConfigType,
  ConfigActivitiesEntity,
  CompanyEntity,
  ModelMasterColorEntity,
  AllocationOrderStatus,
  AOShippingStatus,
  AOListValue,
  AllocationOrderEntity,
  UserVendorTypeMappingEntity,
} from '../../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EntityManager,
  Repository,
  SelectQueryBuilder,
  Not,
  IsNull,
  In,
} from 'typeorm';
import { Request, Response } from 'express';
import { JobsService as ShopJobService } from '../../shop/jobs/jobs.service';
import { AdminUsersService } from '../users/users.service';
import { BranchesService } from '../branches/branches.service';
import { AdminAllocationOrdersService } from '../allocation-orders/allocation-orders.service';
import {
  SuggestPriceDto,
  ExportJobsDto,
  UpdateJobProductDto,
  ExportJobsProductDto,
  ConfirmPriceDto,
} from './dto';
import { omit } from 'lodash';
import { BaseExceptionService } from '../../exceptions';
import {
  ReceiveJobsBody,
  QCStatusBody,
  IConfirmRepairJobBody,
  IMonthlyInspectionBody,
  InspectJobBody,
  AssignRepairBody,
  RejectAOBody,
  LostAOBody,
} from 'contracts';
import {
  BASE_EXCEPTIONS,
  Role,
  JobExportType,
  headerList,
  exportFileName,
  ProductStatus,
  Permission,
  PermissionAction,
} from '../../config';
import { isValidISODate } from '../../../src/utils';
import {
  convertToThaiDateTime,
  getDateFromToday,
  getDateIntervalByMonth,
} from '../../../src/utils/general';
import { convertSlugToHeaderColumn } from '../../../src/utils/job/exportJobReport';
import {
  IExcelTableSlugAllJobStatus,
  shippingStatusCodeMapping,
  getJobsStatusFromCodeStatus,
  qcStatusCodeMapping,
  jobUserColumn,
  jobDateTimeColumn,
  jobMapDatColumn,
  getProductStatusFromCodeStatus,
} from '../../../src/utils/job/excel-export';
import { ContractsService } from '../../../src/shop/contracts/contracts.service';
import {
  getInspectionSqlCase,
  selectInjection,
  ISqlCase,
} from '../../../src/utils/job/monthlyInspection';
import { ExcelManagerService } from '../../excel/excel-manager.service';
import { SystemConfigService } from '../../system-config/system-config.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { reCalculateProduct } from '../../utils/job/queue';
import {
  calculateProductPrices,
  getMargin,
} from '../../utils/job/calculateProduct';
import { Stream } from 'stream';
import { prepareJobsSnapshot } from '../../utils/job/jobsSnapshot';
import { getS3JobUrlPath } from '../../../src/config';
import { S3Service } from '../../storage/s3.service';
import { ExportSapDto } from './dto/export-sap.dto';

@Injectable()
export class AdminJobsService {
  constructor(
    private readonly shopJobService: ShopJobService,
    private readonly systemConfigService: SystemConfigService<string>,
    private readonly adminUserService: AdminUsersService,
    private readonly adminBranchService: BranchesService,
    private readonly allocationOrderService: AdminAllocationOrdersService,
    @InjectRepository(JobEntity)
    private readonly jobsRepo: Repository<JobEntity>,
    @InjectRepository(ImportedVoucherEntity)
    private readonly importedVoucherRepo: Repository<ImportedVoucherEntity>,
    @InjectRepository(JobActivitiesEntity)
    private readonly jobActivitiesRepo: Repository<JobActivitiesEntity>,
    @InjectRepository(DeliveryOrderEntity)
    private readonly deliveryOrderRepo: Repository<DeliveryOrderEntity>,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepo: Repository<SystemConfigEntity>,
    private readonly baseExceptionService: BaseExceptionService,
    @InjectRepository(ExportActivitiesEntity)
    private readonly exportActivitiesRepo: Repository<ExportActivitiesEntity>,
    private readonly excelManagerService: ExcelManagerService,
    @InjectRepository(BranchEntity)
    private readonly branchRepo: Repository<BranchEntity>,
    @InjectRepository(ConfigActivitiesEntity)
    private readonly configActivityRepo: Repository<ConfigActivitiesEntity>,
    @InjectQueue('re-calculate-product-queue')
    private readonly queue: Queue,
    private readonly contractsService: ContractsService,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(ModelMasterColorEntity)
    private readonly modelMasterColorRepo: Repository<ModelMasterColorEntity>,
    @InjectRepository(AllocationOrderEntity)
    private readonly allocationOrderRepo: Repository<AllocationOrderEntity>,
    @InjectRepository(UserVendorTypeMappingEntity)
    private readonly userVendorTypeMappingRepo: Repository<UserVendorTypeMappingEntity>,
    private readonly s3Service: S3Service,
  ) {}

  async getVendorTypeFromUserType(
    userType?: string,
  ): Promise<string | undefined> {
    const mapping = await this.userVendorTypeMappingRepo.findOne({
      where: { userType },
    });

    if (!mapping) {
      throw this.baseExceptionService.exception(
        'USER_TYPE_INVALID',
        `No vendor type found for user type: ${userType}`,
      );
    }

    return mapping.vendorType;
  }

  async buildSearchQuery(
    context: Request & { withUserContext?: WithUserContext },
    listQuery: SelectQueryBuilder<JobEntity>,
  ): Promise<SelectQueryBuilder<JobEntity>> {
    const deviceKey = context.query.deviceKey as string;
    const jobId = context.query.jobId as string;
    const deliveryOrderId = context.query.deliveryOrderId as string;
    const brand = context.query.brand as string;
    const model = context.query.model as string;
    const rom = context.query.rom as string;
    const branch = context.query.branch as string;
    const status = context.query.status as string[];

    const shippingStatus = context.query.shippingStatus as string[];
    const user = context.withUserContext;
    const myJob = context.query.myJob === 'true';
    const searchJobIdOrDeviceKey = context.query
      .searchJobIdOrDeviceKey as string;
    const jobIdOrDeviceKeyLike = context.query.jobIdOrDeviceKeyLike as string;
    const isPartialReceive = /^(true)$/i.test(
      (context.query.partialReceived as string) ?? '',
    );
    const myQCJob = context.query.myQCJob === 'true';
    const qcStatus = context.query.qcStatus as string[];
    const isNotScrap = context.query.isNotScrap === 'true';
    const myRepairJob = context.query.myRepairJob === 'true';
    const isRepairedExist = ['true', 'false'].includes(
      String(context.query.isRepaired),
    );
    const isRepaired = isRepairedExist && context.query.isRepaired === 'true';
    const isTotalInspection = /^(true)$/i.test(
      (context.query.isTotalInspection as string) ?? '',
    );
    const repairedDate = context.query.repairedDate as string;
    const currentGrade = context.query.currentGrade as string[];

    // Construct filter conditions & apply conditions
    const myInspectionJob = context.query.myInspectionJob === 'true';
    const repairAll = context.query.repairAll === 'true';

    const minPriceParam = context.query.minPrice as string;
    const maxPriceParam = context.query.maxPrice as string;
    const minPrice = minPriceParam === 'undefined' ? undefined : minPriceParam;
    const maxPrice = maxPriceParam === 'undefined' ? undefined : maxPriceParam;

    const minUpdatedDateParam = context.query.minUpdatedDate as string;
    const maxUpdatedDateParam = context.query.maxUpdatedDate as string;

    const exportSapStartDate = context.query.startDate as string;
    const exportSapEndDate = context.query.endDate as string;
    const exportSapDateType = context.query.dateType as string;
    this.validateTimeInput(exportSapStartDate, exportSapEndDate, 'export');
    const isIncompleteAO = context.query.isIncompleteAO === 'true';
    const isLessThanAYear =
      context.query.isLessThanAYear === 'true' &&
      minUpdatedDateParam === undefined &&
      maxUpdatedDateParam === undefined;

    const minUpdatedDate =
      minUpdatedDateParam !== undefined
        ? this.validateAndReturnDate(minUpdatedDateParam)
        : minUpdatedDateParam;

    const maxUpdatedDate =
      maxUpdatedDateParam !== undefined
        ? this.validateAndReturnDate(maxUpdatedDateParam)
        : maxUpdatedDateParam;

    const isAFSInspection = context.query.isAFSInspection === 'true';
    const isAFSRepair = context.query.isAFSRepair === 'true';
    const isAFSCustomQuery = isAFSInspection || isAFSRepair;
    this.validateTimeInput(minUpdatedDate, maxUpdatedDate);

    const allocationOrderId = context.query.allocationOrderId as string;
    const allocationOrderShippingStatus = context.query
      .allocationOrderShippingStatus as string[];
    const isConfirmPrice = context.query.isConfirmPrice;
    const isCheckVendorType = context.query.isCheckVendorType as string;

    let vendorType: string | undefined;
    if (isCheckVendorType === 'true') {
      vendorType = await this.getVendorTypeFromUserType(user?.userType);
    }

    const conditions = [
      jobId && `r.jobId ILIKE '%${jobId}%'`,
      deliveryOrderId && `r.deliveryOrderId ILIKE '%${deliveryOrderId}%'`,
      deviceKey && `r.deviceKey ILIKE '%${deviceKey}%'`,
      brand && `r.modelIdentifiers ->> 'brand' = '${brand}'`,
      model && `r.modelIdentifiers ->> 'model' = '${model}'`,
      rom && `r.modelIdentifiers ->> 'rom' = '${rom}'`,
      branch && `r.branchId = '${branch}'`,
      status &&
        this.checkStatus('r.status', status, isAFSInspection || isAFSRepair),
      shippingStatus && this.checkStatus('r.shippingStatus', shippingStatus),
      myJob && `r.adminUserKey = '${user?.userKey}'`,
      searchJobIdOrDeviceKey &&
        `(r.jobId = '${searchJobIdOrDeviceKey}' or r.deviceKey = '${searchJobIdOrDeviceKey}')`,
      jobIdOrDeviceKeyLike &&
        `(r.jobId ILIKE '%${jobIdOrDeviceKeyLike}%' or r.deviceKey ILIKE '%${jobIdOrDeviceKeyLike}%')`,

      isPartialReceive &&
        [
          `r.deliveryOrderId is not null`,
          `r.status = '${JobStatus.PURCHASED}'`,
          `deliveryOrder.status = '${DeliveryOrderStatus.PARTIAL_RECEIVED}'`,
        ].join(' AND '),
      myQCJob && `r.qcBy = '${user?.userKey}'`,
      qcStatus && this.checkStatus('r.qcStatus', qcStatus),
      isNotScrap && `r.qcStatus != 'scrap'`,
      myRepairJob && `r.repairedBy =  '${user?.userKey}'`,
      isRepairedExist && isRepaired && `r.repairListValue is not null`,
      isRepairedExist && !isRepaired && `r.repairListValue is null`,
      isTotalInspection &&
        `(${[
          `(r.status = '${JobStatus.QC_COMPLETED}' AND r.qcStatus = '${QCStatus.SCRAP}')`,
          `(r.status = '${JobStatus.REPAIR_ASSIGNED}' AND r.qcStatus = '${QCStatus.SCRAP}')`,
          this.checkStatus('r.status', [
            JobStatus.REPAIR_COMPLETED,
            JobStatus.INSPECTION_ASSIGNED,
            JobStatus.INSPECTION_COMPLETED,
            JobStatus.INSPECTION_AUTO_COMPLETED,
          ]),
        ].join(' OR ')})`,
      repairedDate &&
        `date_trunc('day', r.repaired_at at time zone 'Asia/Bangkok') = '${this.validateAndReturnDate(
          repairedDate,
        )}'`,
      currentGrade && this.checkStatus('r.currentGrade', currentGrade),
      myInspectionJob &&
        `r.inspectedBy = '${user?.userKey}' and r.assignInspectAt is not null`,
      repairAll &&
        `((r.status = '${JobStatus.QC_COMPLETED}' AND r.qcStatus IN ('fix', 'refurbish')) OR (r.status = '${JobStatus.INSPECTION_FAILED}'))`,
      minPrice && `r.suggested_price >= ${this.validateNumber(minPrice)}`,
      maxPrice && `r.suggested_price <= ${this.validateNumber(maxPrice)}`,
      minUpdatedDate &&
        `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') >= '${minUpdatedDate}'`,
      maxUpdatedDate &&
        `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') <= '${maxUpdatedDate}'`,
      isLessThanAYear &&
        `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') >= '${this.getLastYearDate()}'`,
      exportSapDateType &&
        `Date_trunc('day', "r"."${exportSapDateType}_at" at time zone 'Asia/Bangkok') >= '${exportSapStartDate}'`,
      exportSapDateType &&
        `Date_trunc('day', "r"."${exportSapDateType}_at" at time zone 'Asia/Bangkok') <= '${exportSapEndDate}'`,
      isAFSCustomQuery &&
        this.allJobCustomQuery(isAFSInspection, isAFSRepair, status),
      allocationOrderId && `r.allocationOrderId ILIKE '%${allocationOrderId}%'`,
      allocationOrderShippingStatus &&
        this.checkStatus('r.aoShippingStatus', allocationOrderShippingStatus),
      isConfirmPrice && `r.isConfirmPrice = ${isConfirmPrice === 'true'}`,
      isIncompleteAO &&
        `allocationOrder.status IN ('${AllocationOrderStatus.PARTIAL_RECEIVED}', '${AllocationOrderStatus.REJECT_BY_SHOP}') AND r.aoShippingStatus IN ('${AOShippingStatus.SHIPPED}', '${AOShippingStatus.LOST}')`,
      vendorType && `r.vendorType = '${vendorType}'`,
    ].filter(Boolean);
    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  allJobCustomQuery(
    isAFSInspection: boolean,
    isAFSRepair: boolean,
    status: string[],
  ) {
    const query: string[] = [];
    if (isAFSInspection) {
      query.push(`((r.status = '${JobStatus.QC_COMPLETED}' and r.qc_status = '${QCStatus.SCRAP}') OR 
      (r.status = '${JobStatus.REPAIR_ASSIGNED}' and r.qc_status = '${QCStatus.SCRAP}') OR
      r.status in ('${JobStatus.REPAIR_COMPLETED}','${JobStatus.INSPECTION_ASSIGNED}'))`);
    }

    if (isAFSRepair) {
      query.push(`((r.status = '${JobStatus.QC_COMPLETED}' and r.qc_status != '${QCStatus.SCRAP}') OR 
      (r.status = '${JobStatus.REPAIR_ASSIGNED}' and r.qc_status != '${QCStatus.SCRAP}') OR
      r.status in ('${JobStatus.INSPECTION_FAILED}'))`);
    }

    if (status) {
      query.push(this.checkStatus('r.status', status));
    }

    const result = query.join(' OR ');

    return `(${result})`;
  }
  checkStatus(
    field: string,
    status: string[],
    isNotIncludeStatus?: boolean,
  ): string {
    if (status === undefined) return '';
    if ((status.length === 1 && status[0] === '') || isNotIncludeStatus) {
      return '';
    }

    return `${field} IN ('${status.map((s) => s).join("','")}')`;
  }

  validateTimeInput(min: string, max: string, mode?: string): boolean {
    if (min === undefined && max === undefined) {
      return true;
    }

    if (min === undefined || max === undefined) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Incorrect input: minDate, maxDate should come together.`,
      );
    }

    const minDate = new Date(min);
    const maxDate = new Date(max);

    const diff =
      (maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24); // millisec * sec * minutes * hrs

    if (maxDate.getTime() < minDate.getTime()) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Incorrect input: minDate should less than maxDate.`,
      );
    }

    if (mode === 'export') {
      if (diff > 30) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          `Incorrect input: timespan between min and max date should not more than 31 days.`,
        );
      }
    } else {
      if (diff > 366) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          `Incorrect input: timespan between min and max date should not more than 1 year.`,
        );
      }
    }

    return true;
  }
  validateNumber(number: string): string {
    const isNotInteger = /^[1-9]\d*$/.exec(number) === null;

    if (isNaN(Number(number)) || isNotInteger) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Incorrect input: should be a number.`,
      );
    }

    return number;
  }
  validateAndReturnDate(dateStr: string, format?: string): string {
    if (!isValidISODate(dateStr)) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Date incorrect format. Date format should be YYYY-MM-DDTHH:MM:TT.TTTZ`,
      );
    }

    if (format === 'DDMMYYYY') {
      const newDate = new Date(dateStr);

      return `${newDate.getDate().toString().padStart(2, '0')}/${(
        newDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${newDate.getFullYear()}`;
    }
    return new Date(dateStr).toISOString().split('T')[0];
  }

  getLastYearDate(): string {
    const newDate = new Date();
    newDate.setFullYear(newDate.getFullYear() - 1);

    return newDate.toISOString().split('T')[0];
  }

  sanitizeInputBody(
    data: Partial<JobEntity>,
    isCreated: boolean,
  ): Partial<JobEntity> {
    return this.shopJobService.sanitizeInputBody(data, isCreated);
  }

  computeUpdatePayload(raw: JobEntity, data: Partial<JobEntity>) {
    if (
      [JobStatus.REJECT_BY_SHOP, JobStatus.REJECT_BY_CUSTOMER].includes(
        raw.status,
      )
    ) {
      throw this.baseExceptionService.exception(
        'UNAVAILABLE_JOB',
        `Requested job is canceled`,
      );
    }

    return this.shopJobService.computeUpdatePayload(raw, data);
  }

  preSave(
    man: EntityManager,
    data: Partial<JobEntity>,
    isCreated: boolean,
  ): Partial<JobEntity> {
    // unique key arrays, omit before process an update action
    return isCreated
      ? data
      : man.create(
          JobEntity,
          omit(data, [
            'adminUser',
            'branch',
            'qcUser',
            'repairedUser',
            'inspectedUser',
            'modelMaster',
            'color',
          ]),
        );
  }

  async jobCount(context: Request & { withUserContext?: WithUserContext }) {
    const user = context.withUserContext;

    const vendorType = await this.getVendorTypeFromUserType(user?.userType);

    const query_all_pending = {
      status: JobStatus.QUOTE_REQUESTED,
      vendorType,
    };
    const query_all_estimating = {
      status: JobStatus.ESTIMATE_PRICE_PROCESSING,
      vendorType,
    };
    const query_my_estimating = {
      status: JobStatus.ESTIMATE_PRICE_PROCESSING,
      adminUserKey: user?.userKey,
      vendorType,
    };

    return await Promise.all([
      this.jobsRepo.count({
        where: query_all_pending,
      }),
      this.jobsRepo.count({
        where: query_all_estimating,
      }),
      this.jobsRepo.count({
        where: query_my_estimating,
      }),
    ]);
  }

  async findAndValidateJobVendorType(
    id: string,
    user: WithUserContext,
  ): Promise<JobEntity | null> {
    const job = await this.jobsRepo.findOneBy({
      jobId: id,
    });
    if (!job) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `There is no such jobId`,
      );
    }
    const vendorType = await this.getVendorTypeFromUserType(user.userType);

    if (job.vendorType !== vendorType) {
      throw this.baseExceptionService.exception(
        'USER_TYPE_INVALID',
        `This job is not assigned to your vendor type`,
      );
    }

    return job;
  }

  async prepareAssignJob(
    id: string,
    user: WithUserContext,
  ): Promise<JobEntity> {
    // Find and validate the job
    await this.findAndValidateJobVendorType(id, user);
    // Create the JobEntity
    const jobEntity = new JobEntity();
    jobEntity.updatedBy = user.userKey;
    jobEntity.adminUserKey = user.userKey;
    jobEntity.adminUserName = user.name;
    jobEntity.status = JobStatus.ESTIMATE_PRICE_PROCESSING;
    jobEntity.assignedAt = new Date();

    return jobEntity;
  }

  async prepareSuggestPrice(
    id: string,
    body: SuggestPriceDto,
    user: WithUserContext,
  ): Promise<JobEntity> {
    // Find and validate the job
    const job = await this.findAndValidateJobVendorType(id, user);

    const min = 1;
    const max = job?.modelTemplate.modelMasterGrades[0]?.purchasePrice;

    if (max !== undefined) {
      const maxAsNumber = parseFloat(max);
      const suggestedPrice = body.suggestedPrice;
      if (
        suggestedPrice === undefined ||
        suggestedPrice < min ||
        suggestedPrice > maxAsNumber
      ) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Suggested price is not in the price range`,
        );
      }

      const voucher = await this.importedVoucherRepo.findOneBy({
        voucherValue: suggestedPrice,
        contractId: IsNull(),
      });

      if (!voucher) {
        throw this.baseExceptionService.exception(
          'VOUCHER_NOT_FOUND',
          `Voucher not found`,
        );
      }
    }

    if (!body.grade) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Please provide grade for this product`,
      );
    }

    const jobEntity = new JobEntity();
    jobEntity.updatedBy = user.userKey;
    jobEntity.adminUserKey = user.userKey;
    jobEntity.adminUserName = user.name;
    jobEntity.status = JobStatus.PRICE_ESTIMATED;
    jobEntity.suggestedPrice = body.suggestedPrice;
    jobEntity.currentGrade = body.grade;
    jobEntity.adminCheckListValues = body.adminCheckListValues;
    jobEntity.estimatedAt = new Date();
    jobEntity.estimatedGrade = body.grade;

    return jobEntity;
  }
  async defaultPrepareJob(body: Partial<JobEntity>): Promise<JobEntity> {
    return this.shopJobService.defaultPrepareJob(body);
  }

  async insertJobActivities(
    jobEntity: JobEntity,
    activityType: JobActivitiesType,
    detail: JobActivityDetail,
    user: WithUserContext,
  ) {
    return this.shopJobService.insertJobActivities(
      jobEntity,
      activityType,
      detail,
      user,
    );
  }

  shippingStatus(): { label: string; value: JobShippingStatus }[] {
    const listShippingStatus: { label: string; value: JobShippingStatus }[] =
      [];
    const label = {
      [JobShippingStatus.RECEIVED]: 'รับสินค้า',
      [JobShippingStatus.RECEIVED_OTHER]: 'รับสินค้าแบบอื่นๆ',
      [JobShippingStatus.RECEIVED_WITH_CONDITION]: 'รับสินค้าแบบมีเงื่อนไข',
    };
    for (const key in JobShippingStatus) {
      const statusCode = JobShippingStatus[key];
      if (label[statusCode]) {
        listShippingStatus.push({
          label: label[statusCode],
          value: statusCode,
        });
      }
    }
    return listShippingStatus;
  }

  async updateReceiveJobs(jobs: ReceiveJobsBody[], user: WithUserContext) {
    const queryJobData = await this.validateReceiveJobs(jobs);

    const mapJobsKeyValue = {};
    for (const job of jobs) {
      mapJobsKeyValue[job.id] = job;
    }

    const countUpdate: { [key: string]: number } = {};
    // update job
    const saveJobList: JobEntity[] = [];
    for (let saveData of queryJobData) {
      saveData.updatedBy = user.userKey;
      saveData.receiverUserKey = user.userKey;
      saveData.receivedAt = DateTime.now().toJSDate();
      saveData.receiverUserKey = user.userKey;
      saveData.shippingStatus = <JobShippingStatus>(
        mapJobsKeyValue[saveData.jobId].shippingStatus
      );

      saveData.status = JobStatus.RECEIVED;

      if (
        saveData.shippingStatus === JobShippingStatus.RECEIVED_OTHER ||
        saveData.shippingStatus === JobShippingStatus.RECEIVED_WITH_CONDITION
      ) {
        saveData.receivingRemark = mapJobsKeyValue[saveData.jobId].remark;
      }

      if (saveData.deliveryOrderId) {
        countUpdate[saveData.deliveryOrderId] = countUpdate[
          saveData.deliveryOrderId
        ]
          ? countUpdate[saveData.deliveryOrderId] + 1
          : 1;
      }

      saveJobList.push(saveData);
    }

    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      await queryRunner.manager.save(saveJobList);

      const deliveryOrderList = [
        ...new Set(queryJobData.map((job) => job.deliveryOrderId)),
      ];
      const queryUpdatedJob = await this.getQueryJobByDO(deliveryOrderList);
      const queryDeliveryOrderData =
        await this.getQueryDeliveryOrderData(deliveryOrderList);
      const prepareCountDO = deliveryOrderList.reduce((key, value) => {
        if (value) {
          key[value] = 0;
        }
        return key;
      }, {});

      for (const job of queryUpdatedJob) {
        if (job.deliveryOrderId) {
          prepareCountDO[job.deliveryOrderId]++;
        }
      }

      const saveDOList: DeliveryOrderEntity[] = [];
      for (const deliveryOrder of queryDeliveryOrderData) {
        const countReceived =
          Number(prepareCountDO[deliveryOrder.deliveryOrderId]) +
          countUpdate[deliveryOrder.deliveryOrderId];

        if (countReceived === deliveryOrder.quantity) {
          deliveryOrder.status = DeliveryOrderStatus.DELIVERY_SUCCESSFUL;
          deliveryOrder.receivedDate = DateTime.now().toJSDate();
        } else if (
          deliveryOrder.transporterName &&
          deliveryOrder.transporterMobileNumber
        ) {
          deliveryOrder.status = DeliveryOrderStatus.PARTIAL_RECEIVED;
        }

        saveDOList.push(deliveryOrder);
      }

      await this.deliveryOrderRepo.save(saveDOList);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return null;
  }

  async getQueryJobByDO(deliveryOrderList: any[]) {
    const statusList = [
      JobShippingStatus.RECEIVED,
      JobShippingStatus.RECEIVED_WITH_CONDITION,
      JobShippingStatus.RECEIVED_OTHER,
    ];
    const createCustomQuery = this.jobsRepo
      .createQueryBuilder('job')
      .where(
        'job.delivery_order_id IN(:...ids) AND job.shipping_status in (:...status)',
        {
          ids: deliveryOrderList,
          status: statusList,
        },
      );

    const result: JobEntity[] = await createCustomQuery.getMany();

    return result;
  }

  async getQueryDeliveryOrderData(deliveryOrderList: any[]) {
    const createCustomQuery = this.deliveryOrderRepo
      .createQueryBuilder('delivery_order')
      .where('delivery_order.delivery_order_id IN(:...ids)', {
        ids: deliveryOrderList,
      });

    const result: DeliveryOrderEntity[] = await createCustomQuery.getMany();

    return result;
  }

  async validateReceiveJobs(jobs: ReceiveJobsBody[]) {
    //check max length
    if (jobs.length > 20) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Over max jobs limit (20)`,
      );
    }

    jobs.forEach((job) => {
      // validate require field
      if (
        job.id === undefined ||
        job.shippingStatus === undefined ||
        job.remark === undefined
      ) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `missing required field(s)`,
        );
      }

      // validate shipping status
      if (
        !(
          job.shippingStatus === JobShippingStatus.RECEIVED ||
          job.shippingStatus === JobShippingStatus.RECEIVED_OTHER ||
          job.shippingStatus === JobShippingStatus.RECEIVED_WITH_CONDITION
        )
      ) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `invalid shipping status - the status must be: 10_RECEIVED, 11_RECEIVED_WITH_CONDITION, 12_RECEIVED_OTHER`,
        );
      }

      // validate shippingstatus and remark
      if (
        job.shippingStatus === JobShippingStatus.RECEIVED_OTHER ||
        job.shippingStatus === JobShippingStatus.RECEIVED_WITH_CONDITION
      ) {
        if (job.remark === null) {
          throw this.baseExceptionService.exception(
            'INVALID_INPUT_FOR_UPDATE_JOB',
            `remark should not null - shipping status: 11_RECEIVED_WITH_CONDITION, 12_RECEIVED_OTHER`,
          );
        }

        if (job.remark.length > 200) {
          throw this.baseExceptionService.exception(
            'INVALID_INPUT_FOR_UPDATE_JOB',
            `Remark, over max character limit (200)`,
          );
        }
      }

      // validate shippingstatus and remark
      if (job.shippingStatus === JobShippingStatus.RECEIVED) {
        if (job.remark !== null) {
          throw this.baseExceptionService.exception(
            'INVALID_INPUT_FOR_UPDATE_JOB',
            `remark should null - shipping status: 10_RECEIVED`,
          );
        }
      }
    });

    // check actual jobs
    const jobIDList: string[] = [...new Set(jobs.map((job) => job.id))];
    if (jobIDList.length !== jobs.length) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Duplicate id not allowed`,
      );
    }

    const createCustomQuery = this.jobsRepo
      .createQueryBuilder('job')
      .where('job.jobId IN(:...ids) AND job.status = :status', {
        ids: jobIDList,
        status: JobStatus.PURCHASED,
      });

    const result: JobEntity[] = await createCustomQuery.getMany();
    if (result.length !== jobs.length) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status - the status must be: 40_PURCHASED`,
      );
    }

    return result;
  }

  async updateQCStatus(
    jobId: string,
    body: QCStatusBody,
    user: WithUserContext,
  ) {
    // validate body not null
    if (!Object.keys(body).includes('status')) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `missing required field: status`,
      );
    }

    // validate shipping status
    if (
      !(
        body.status === QCStatus.FIX ||
        body.status === QCStatus.REFURBISH ||
        body.status === QCStatus.SCRAP
      )
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `invalid qc status - the status must be: fix, refurbish, scrap`,
      );
    }

    // validate job status and prepare init job data to save
    const createCustomQuery = this.jobsRepo
      .createQueryBuilder('job')
      .where('job.jobId = :id AND job.status = :status', {
        id: jobId,
        status: JobStatus.RECEIVED,
      });

    const job = await createCustomQuery.getOne();
    if (!job) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status - the status must be: 50_RECEIVED`,
      );
    }

    // append data to job before save
    let saveJob = job;
    const now = DateTime.now().toJSDate();
    saveJob.status = JobStatus.QC_COMPLETED;
    saveJob.qcStatus = body.status;
    saveJob.qcBy = user.userKey;
    saveJob.qcAt = now;
    saveJob.updatedBy = user.userKey;

    if (body.status === QCStatus.SCRAP) {
      const { repairListValue } = job;
      const newItemRepair: RepairHx = {
        type: QCStatus.SCRAP,
        detail: '-',
        by: { key: user.userKey, name: user.name ?? '' },
        at: saveJob.qcAt,
      };

      const newRepairListValue: RepairHx[] = repairListValue
        ? [...repairListValue, newItemRepair]
        : [newItemRepair];

      saveJob.repairListValue = newRepairListValue;

      saveJob.repairedAt = now;
      saveJob.currentGrade = null;
    }

    await this.jobsRepo.save(saveJob);
    return null;
  }

  async assignRepairJob(
    jobId: string,
    user: WithUserContext,
    body: AssignRepairBody,
  ) {
    const qcStatusFromBody = body.qcStatus;

    const jobEntity = await this.jobsRepo.findOne({
      where: { jobId: jobId },
    });

    if (!jobEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    if (
      !(
        jobEntity.status === JobStatus.QC_COMPLETED ||
        jobEntity.status === JobStatus.INSPECTION_FAILED
      )
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status. Job status must be ${JobStatus.QC_COMPLETED} or ${JobStatus.INSPECTION_FAILED}`,
      );
    }

    if (jobEntity.status === JobStatus.QC_COMPLETED && qcStatusFromBody) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `QC Completed should not provide qcStatus in body`,
      );
    }

    if (
      jobEntity.status === JobStatus.QC_COMPLETED &&
      jobEntity.qcStatus === QCStatus.SCRAP
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid qc status. QC status must be ${QCStatus.FIX} or ${QCStatus.REFURBISH}`,
      );
    }

    if (jobEntity.status === JobStatus.INSPECTION_FAILED) {
      if (!qcStatusFromBody) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Inspection Failed should provide qcStatus in body`,
        );
      }

      if (
        qcStatusFromBody !== QCStatus.FIX &&
        qcStatusFromBody !== QCStatus.REFURBISH
      ) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Invalid qcStatus Body. QC status must be ${QCStatus.FIX} or ${QCStatus.REFURBISH}`,
        );
      }

      jobEntity.repairListValue = jobEntity.repairListValue
        ? jobEntity.repairListValue
        : [];

      jobEntity.qcStatus = qcStatusFromBody;
    }

    jobEntity.status = JobStatus.REPAIR_ASSIGNED;
    jobEntity.assignRepairAt = new Date();
    jobEntity.repairedBy = user.userKey;
    jobEntity.updatedBy = user.userKey;
    await this.jobsRepo.save(jobEntity);
    return null;
  }

  async confirmRepairJob({
    id,
    user,
    body,
  }: {
    id: string;
    user: WithUserContext;
    body: IConfirmRepairJobBody;
  }) {
    // --- find job
    const jobEntity = await this.jobsRepo.findOne({
      where: { jobId: id },
    });

    if (!jobEntity) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA', {
        type: 'JOB_NOT_FOUND',
        detail: 'Not found job',
      });
    }

    const {
      status: jobStatus,
      qcStatus,
      repairedBy,
      repairListValue,
      inspectListValue,
      estimatedGrade,
    } = jobEntity;
    const { detail, cost, grade, type } = body;
    const { userKey: currentUser, name: currentUserName } = user;

    // --- validate data and action permission
    const regexPriceForTest = /^[0-9]*(\.[0-9]{0,2})?$/;
    let errorMsgType = '';
    let errorText = '';

    if (!jobStatus || !qcStatus || !repairedBy) {
      errorMsgType = 'JOB_STATUS_NOT_MATCH_ACTION';
      errorText = `This job has some problem not match requirement before update`;
    } else if (jobStatus !== JobStatus.REPAIR_ASSIGNED) {
      errorMsgType = 'JOB_STATUS_NOT_MATCH_ACTION';
      errorText = `Job status must be ${JobStatus.REPAIR_ASSIGNED}`;
    } else if (![QCStatus.FIX, QCStatus.REFURBISH].includes(qcStatus)) {
      errorMsgType = 'QC_STATUS_NOT_MATCH_ACTION';
      errorText = `Qc status must be ${QCStatus.FIX} or ${QCStatus.REFURBISH}`;
    } else if (qcStatus === QCStatus.REFURBISH && type !== 'confirm') {
      errorMsgType = 'QC_STATUS_NOT_MATCH_ACTION';
      errorText = 'Type must be confirm';
    } else if (
      qcStatus === QCStatus.FIX &&
      type === 'scrap' &&
      inspectListValue?.length
    ) {
      errorMsgType = 'QC_STATUS_NOT_MATCH_ACTION';
      errorText = 'Type must not be scrap';
    } else if (
      ['refurbish', 'confirm'].includes(type) &&
      !(cost !== undefined && grade)
    ) {
      errorMsgType = 'FIELD_DATA_NOT_MATCH';
      errorText = 'This type must have grade and cost';
    } else if (type === 'scrap' && (cost || grade)) {
      errorMsgType = 'FIELD_DATA_NOT_MATCH';
      errorText = 'This type must not have grade or cost';
    } else if (!detail || detail.length > 200) {
      errorMsgType = 'DATA_FORMAT_FAIL';
      errorText = 'Detail must not be empty and not over 200 char';
    } else if (
      cost !== undefined &&
      (isNaN(cost) || cost < 0 || !regexPriceForTest.test(cost.toString()))
    ) {
      errorMsgType = 'DATA_FORMAT_FAIL';
      errorText = 'Cost must be number >=0 and max 2 decimal';
    } else if (grade && ![estimatedGrade, 'D'].includes(grade)) {
      errorMsgType = 'DATA_FORMAT_FAIL';
      errorText = `Grade must be ${
        estimatedGrade === 'D' ? 'D' : `${estimatedGrade} or D`
      }`;
    } else if (repairedBy !== currentUser) {
      errorMsgType = 'USER_NOT_MATCH';
      errorText = 'Update user must be repairedBy user';
    }

    if (errorMsgType) {
      let error: keyof typeof BASE_EXCEPTIONS = 'INVALID_INPUT_FOR_UPDATE_JOB';
      if (errorMsgType === 'USER_NOT_MATCH') {
        error = 'INVALID_JOB_PERMISSION';
      }

      throw this.baseExceptionService.exception(error, {
        type: errorMsgType,
        detail: errorText,
      });
    }

    // --- update data
    const confirmType: QCStatus =
      type === 'scrap' ? QCStatus.SCRAP : (qcStatus as QCStatus);

    const repairedAt = new Date();

    const newItemRepair: RepairHx = {
      type: confirmType,
      detail,
      by: { key: currentUser, name: currentUserName ?? '' },
      at: repairedAt,
      assignedAt: jobEntity.assignRepairAt,
    };

    if (type === 'confirm') {
      jobEntity.status = JobStatus.REPAIR_COMPLETED;
      newItemRepair.grade = grade as IRepairHxGrade;
      jobEntity.currentGrade = grade;
      newItemRepair.cost = cost;
    } else if (type === 'refurbish') {
      jobEntity.qcStatus = QCStatus.REFURBISH;
      newItemRepair.grade = grade as IRepairHxGrade;
      jobEntity.currentGrade = grade;
      jobEntity.assignRepairAt = repairedAt;
      newItemRepair.cost = cost;
    } else if (type === 'scrap') {
      jobEntity.qcStatus = QCStatus.SCRAP;
      jobEntity.currentGrade = null;
    }

    const newRepairListValue: RepairHx[] = repairListValue
      ? [...repairListValue, newItemRepair]
      : [newItemRepair];
    jobEntity.repairListValue = newRepairListValue;
    jobEntity.repairedAt = repairedAt;
    jobEntity.updatedBy = user.userKey;

    await this.jobsRepo.save(jobEntity);
    return null;
  }

  async assignInspectJob(jobId: string, user: WithUserContext) {
    const jobEntity = await this.jobsRepo.findOne({
      where: { jobId: jobId },
    });

    if (!jobEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    if (
      !(
        jobEntity.status === JobStatus.REPAIR_COMPLETED ||
        ((jobEntity.status === JobStatus.QC_COMPLETED ||
          jobEntity.status === JobStatus.REPAIR_ASSIGNED) &&
          jobEntity.qcStatus === QCStatus.SCRAP)
      )
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status.`,
      );
    }

    jobEntity.status = JobStatus.INSPECTION_ASSIGNED;
    jobEntity.assignInspectAt = new Date();
    jobEntity.inspectedBy = user.userKey;
    jobEntity.updatedBy = user.userKey;
    await this.jobsRepo.save(jobEntity);
    return null;
  }

  async getMinInspectDate() {
    const targetJobList = await this.jobsRepo.find({
      select: { repairedAt: true },
      where: [
        {
          repairedAt: Not(IsNull()),
          status: In([
            JobStatus.REPAIR_COMPLETED,
            JobStatus.INSPECTION_ASSIGNED,
            JobStatus.INSPECTION_COMPLETED,
            JobStatus.INSPECTION_AUTO_COMPLETED,
          ]),
        },
        {
          repairedAt: Not(IsNull()),
          status: In([JobStatus.QC_COMPLETED, JobStatus.REPAIR_ASSIGNED]),
          qcStatus: QCStatus.SCRAP,
        },
      ],
      order: {
        repairedAt: 'ASC',
      },
    });

    if (targetJobList.length) {
      const minDateX = targetJobList[0].repairedAt;
      const minDate = minDateX ? convertToThaiDateTime(minDateX) : null;
      const todayTime = DateTime.local({ zone: 'Asia/Bangkok' }).startOf('day');

      if (minDate && minDate < todayTime) {
        return minDateX;
      }
    }

    return null;
  }

  async getMonthlyInspect(month: IMonthlyInspectionBody) {
    const [currentYear, currentMonth, dateToday] = getDateFromToday();
    const yesterday = getDateFromToday(-1);
    const yesterdayDate = yesterday[3];

    const Year = Number(month.year);
    const Month = Number(month.month);

    let error: null | { type: string; text: string } = null;

    if (isNaN(Month) || Month < 1 || Month > 12) {
      error = { type: 'MONTH_ERROR', text: 'Month must be number 1-12' };
    } else if (isNaN(Year) || Year < 1000 || Year > 9999) {
      error = { type: 'YEAR_ERROR', text: 'Year must be 4 digit number' };
    } else if (
      currentYear < Year ||
      (currentYear === Year && currentMonth < Month)
    ) {
      error = {
        type: 'TIME_ERROR',
        text: 'Month-Year must less than or equal to this month',
      };
    } else if (
      currentYear === Year &&
      currentMonth === Month &&
      dateToday === 1
    ) {
      error = {
        type: 'TIME_ERROR',
        text: 'Can not select this month when date today is 1',
      };
    }

    if (error !== null) {
      throw this.baseExceptionService.exception('BODY_PAYLOAD_INVALID', error);
    }

    const dateInterval = getDateIntervalByMonth(month);
    const startDate = dateInterval[0];
    let endDate = dateInterval[1];
    if (
      currentYear === Number(month.year) &&
      currentMonth === Number(month.month)
    ) {
      endDate = yesterdayDate;
    }

    // --- calculate sql string
    let querySQL: string = '';
    let querySelectSQL: string = 'SELECT ';

    const sqlCase = getInspectionSqlCase(startDate, endDate);

    sqlCase.forEach((caseQuery: ISqlCase, index: number) => {
      const { columnName, sqlInject } = caseQuery;
      if (index !== 0) {
        querySelectSQL += ', ';
        querySQL += ' LEFT JOIN ';
      } else {
        querySQL += ' FROM ';
      }
      querySelectSQL += `${caseQuery.select} as ${columnName}`;
      querySQL += selectInjection(index, sqlInject, columnName);
    });
    querySQL = `${querySelectSQL} ${querySQL} ORDER BY date`;

    // --- result manage
    const queryResult = await this.jobsRepo.query(querySQL);

    const monthlyInspectData = queryResult.map((dataByDate) => {
      const {
        date,
        all_product,
        a_grade_product,
        other_grade_product,
        scrap_product,
        not_complete_product,
      } = dataByDate;
      return {
        date,
        allProduct: Number(all_product),
        aGradeProduct: Number(a_grade_product),
        otherGradeProduct: Number(other_grade_product),
        scrapProduct: Number(scrap_product),
        completeStatus: !not_complete_product,
      };
    });

    return { monthlyInspectData, startDate, endDate };
  }

  async updateInspectedJob(
    jobId: string,
    user: WithUserContext,
    body: InspectJobBody,
  ) {
    const inspectionResult = body.inspectionResult;
    const inspectionDetail = body.inspectionDetail;
    const timeInspectedAt = new Date();
    const jobEntity = await this.jobsRepo.findOne({
      where: { jobId: jobId },
      relations: ['modelMaster'],
    });

    if (!jobEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    if (jobEntity.status !== JobStatus.INSPECTION_ASSIGNED) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status. Job status must be ${JobStatus.INSPECTION_ASSIGNED}.`,
      );
    }

    if (jobEntity.inspectedBy !== user.userKey) {
      throw this.baseExceptionService.exception(
        'UNAUTHORIZED',
        `user (${user.name}) does not have permission to update this job.`,
      );
    }

    if (!jobEntity.repairedAt) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `invalid data`,
      );
    }

    if (inspectionResult === 'pass') {
      if (inspectionDetail) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Invalid body. If pass should not have detail`,
        );
      }
      jobEntity.status = JobStatus.INSPECTION_COMPLETED;
    }

    if (inspectionResult === 'fail') {
      const inspectListValueCount = jobEntity.inspectListValue
        ? jobEntity.inspectListValue.length
        : 0;

      if (!inspectionDetail) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Detail is required when result is fail.`,
        );
      }

      if (inspectListValueCount >= 2) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Cannot mark as fail more than 2 times`,
        );
      }

      jobEntity.status = JobStatus.INSPECTION_FAILED;
    }

    // stamp history
    const inspectHx: InspectHx[] = [];

    if (jobEntity.inspectListValue) {
      inspectHx.push(...jobEntity.inspectListValue);
    }

    inspectHx.push({
      at: timeInspectedAt,
      by: { key: user.userKey, name: user.name ?? '' },
      isPassed: inspectionResult === 'pass',
      isQCScrap: jobEntity.qcStatus === 'scrap',
      detail: inspectionDetail,
    });

    const operationCostConfig = await this.systemConfigRepo.findOne({
      where: { configKey: 'operation_cost' },
    });

    // save current job
    if (operationCostConfig?.data) {
      const {
        wholeSaleMargin,
        wholeSalePrice,
        marginWholeSaleBaht,
        retailMargin,
        retailPrice,
        marginRetailBaht,
        costPrice,
      } = calculateProductPrices(jobEntity, operationCostConfig.data);
      jobEntity.wholeSaleMargin = wholeSaleMargin;
      jobEntity.wholeSalePrice = wholeSalePrice;
      jobEntity.marginWholeSaleBaht = marginWholeSaleBaht;
      jobEntity.retailMargin = retailMargin;
      jobEntity.retailPrice = retailPrice;
      jobEntity.marginRetailBaht = marginRetailBaht;
      jobEntity.costPrice = costPrice;
    }
    jobEntity.inspectListValue = inspectHx;
    jobEntity.inspectedAt = timeInspectedAt;
    jobEntity.updatedBy = user.userKey;
    await this.jobsRepo.save(jobEntity);

    const queryDate = new Date(jobEntity.repairedAt.getTime());
    const strQueryDate = `${queryDate.getFullYear()}-${(
      queryDate.getMonth() + 1
    )
      .toString()
      .padStart(2, '0')}-${queryDate.getDate().toString().padStart(2, '0')}`;

    const doAutoComplete = await this.checkAutoCompleteJob(
      jobEntity,
      strQueryDate,
    );

    if (doAutoComplete) {
      const autoCompletedJob = await this.jobsRepo
        .createQueryBuilder('job')
        .innerJoinAndSelect('job.modelMaster', 'modelMaster')
        .where(
          `job.status in ('${JobStatus.INSPECTION_ASSIGNED}', '${JobStatus.REPAIR_COMPLETED}') AND job.currentGrade = 'A' AND
        date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = '${strQueryDate} 00:00:00'`,
        )
        .getMany();

      autoCompletedJob.forEach((job) => {
        job.status = JobStatus.INSPECTION_AUTO_COMPLETED;
        job.inspectedAt = timeInspectedAt;
        job.inspectedBy = null;

        // stamp history
        const autoInspectHx: InspectHx[] = [];

        if (job.inspectListValue) {
          autoInspectHx.push(...job.inspectListValue);
        }

        autoInspectHx.push({
          at: timeInspectedAt,
          by: { key: 'system', name: 'system' },
          isPassed: true,
          isQCScrap: false,
        });

        job.inspectListValue = autoInspectHx;
        if (operationCostConfig?.data) {
          const {
            wholeSaleMargin,
            wholeSalePrice,
            marginWholeSaleBaht,
            retailMargin,
            retailPrice,
            marginRetailBaht,
            costPrice,
          } = calculateProductPrices(job, operationCostConfig.data);
          job.wholeSaleMargin = wholeSaleMargin;
          job.wholeSalePrice = wholeSalePrice;
          job.marginWholeSaleBaht = marginWholeSaleBaht;
          job.retailMargin = retailMargin;
          job.retailPrice = retailPrice;
          job.marginRetailBaht = marginRetailBaht;
          job.costPrice = costPrice;
        }
      });

      await this.jobsRepo.save(autoCompletedJob);
    }

    return null;
  }

  async getTransactionInspection(jobId: string) {
    const job = await this.jobsRepo.findOne({
      where: { jobId },
      relations: ['color'],
    });

    if (!job) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    const { companyId, status, repairListValue, inspectListValue } = job;

    if (
      ![
        JobStatus.INSPECTION_COMPLETED,
        JobStatus.INSPECTION_AUTO_COMPLETED,
      ].includes(status)
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_JOB_FOR_ACTION',
        `Job status not match.`,
      );
    }

    if (!repairListValue?.length || !inspectListValue?.length) {
      throw this.baseExceptionService.exception('INVALID_JOB_FOR_ACTION');
    }

    const company = await this.companyRepo.findOne({
      where: { companyId },
    });

    if (!company?.logoPath) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Company image not found - invalid url',
      );
    }

    return await this.contractsService.getTransactionInspection(
      job,
      company.logoPath,
    );
  }

  async checkAutoCompleteJob(job: JobEntity, strQueryDate: string) {
    const query = `
    SELECT 
      count(
        CASE WHEN (
          status IN (
            '70_REPAIR_COMPLETED', '75_INSPECTION_ASSIGNED', 
            '80_INSPECTION_COMPLETED') 
          AND current_grade = 'A' 
          AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = date_trunc('day', '${strQueryDate}' at time zone 'Asia/Bangkok')
        ) THEN 1 END
      ) AS grade_a_all, 
      count(
        CASE WHEN (
          status IN ('80_INSPECTION_COMPLETED') 
          AND current_grade = 'A'
          AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = date_trunc('day', '${strQueryDate}' at time zone 'Asia/Bangkok')
        ) THEN 1 END
      ) AS grade_a_completed
    FROM 
      core.job

    `;

    const result = await this.jobsRepo.query(query);

    const gradeATotal: number = parseInt(result[0].grade_a_all);
    const gradeACount5Percent: number = Math.ceil(gradeATotal * 0.05);
    const gradeACompleted: number = parseInt(result[0].grade_a_completed);

    return gradeACount5Percent - gradeACompleted < 1;
  }

  async getMenuCount(user: WithUserContext) {
    const userPermissions = user.permissions?.find(
      (permission) => permission.branchId === 'ADMIN_BRANCH',
    );

    const hasViewInspectionAllPermission = userPermissions?.permission.includes(
      `${Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW}`,
    );

    const hasViewInspectionMyPermission = userPermissions?.permission.includes(
      `${Permission.CMS_INSPECTION_MY + PermissionAction.VIEW}`,
    );

    const hasViewRepairPendingPermission = userPermissions?.permission.includes(
      `${Permission.CMS_REPAIR_PENDING + PermissionAction.VIEW}`,
    );

    const hasViewRepairMyPermission = userPermissions?.permission.includes(
      `${Permission.CMS_REPAIR_MY + PermissionAction.VIEW}`,
    );

    const userKey = user.userKey;
    let sqlSelectStatement = '';
    let sqlForInspectionRole: string = '';
    let totalItem: number = 0;

    // prepare query statment

    const today = getDateFromToday();

    if (hasViewInspectionAllPermission || hasViewInspectionMyPermission) {
      sqlForInspectionRole = `
        SELECT
        DATE(repaired_at at time zone 'Asia/Bangkok') AS datetime, 
          count(
            CASE WHEN (
              date_trunc('day', repaired_at at time zone 'Asia/Bangkok') < '${today[3]}'
              AND status IN (
                '70_REPAIR_COMPLETED', '75_INSPECTION_ASSIGNED', 
                '80_INSPECTION_COMPLETED'
              ) 
              AND current_grade = 'A'
            ) THEN 1 END
          ) AS grade_a_all, 
          count(
            CASE WHEN (
              date_trunc('day', repaired_at at time zone 'Asia/Bangkok') < '${today[3]} 00:00:00'
              AND status IN ('80_INSPECTION_COMPLETED') 
              AND current_grade = 'A'
            ) THEN 1 END
          ) AS grade_a_completed, 
          count(
            CASE WHEN (
              date_trunc('day', repaired_at at time zone 'Asia/Bangkok') < '${today[3]} 00:00:00'
              AND status IN (
                '70_REPAIR_COMPLETED', '75_INSPECTION_ASSIGNED'
              ) 
              AND current_grade != 'A' 
              AND qc_status != 'scrap'
            ) THEN 1 END
          ) AS grade_others, 
          count(
            CASE WHEN (
              date_trunc('day', repaired_at at time zone 'Asia/Bangkok') < '${today[3]} 00:00:00'
              AND status IN (
                '60_QC_COMPLETED', '65_REPAIR_ASSIGNED', '75_INSPECTION_ASSIGNED'
              ) 
              AND qc_status = 'scrap'
            ) THEN 1 END
          ) AS grade_scrap, 
          COUNT(
            CASE WHEN (
              status = '75_INSPECTION_ASSIGNED' 
              AND inspected_by = '${userKey}'
            ) THEN 1 END
          ) AS MY_INSPECTION
          FROM 
            core.job 
          GROUP BY 
            DATE(repaired_at at time zone 'Asia/Bangkok') 
          ORDER BY 
            DATE(repaired_at at time zone 'Asia/Bangkok');`;

      sqlSelectStatement = `${sqlSelectStatement}
        COUNT(
          CASE WHEN (
            status = '75_INSPECTION_ASSIGNED' AND inspected_by = '${userKey}'
            ) THEN 1 END
          ) AS MY_INSPECTION,
          `;
    }

    if (hasViewRepairPendingPermission || hasViewRepairMyPermission) {
      sqlSelectStatement = `${sqlSelectStatement}
        COUNT(
          CASE WHEN (
            (
              STATUS = '60_QC_COMPLETED' 
              AND QC_STATUS IN ('fix', 'refurbish')
            ) 
            OR (
              STATUS = '60_QC_COMPLETED' 
              AND QC_STATUS = 'fix'
            ) 
            OR (
              STATUS = '60_QC_COMPLETED' 
              AND QC_STATUS = 'refurbish'
            ) 
            OR (STATUS = '79_INSPECTION_FAILED')
          ) THEN 1 END
        ) AS REPAIR,
        COUNT(
          CASE WHEN (
            (STATUS = '65_REPAIR_ASSIGNED' AND QC_STATUS IN ('fix', 'refurbish'))
            AND REPAIRED_BY = '${userKey}'
          ) THEN 1 END
        ) AS MY_REPAIR,`;
    }

    // query for total inspection count
    if (sqlForInspectionRole !== '') {
      const results = await this.jobsRepo.query(sqlForInspectionRole);
      results.forEach((data) => {
        if (data.datetime !== null) {
          const gradeATotal: number = parseInt(data.grade_a_all);
          const gradeACount5Percent: number = Math.ceil(gradeATotal * 0.05);
          const gradeACompleted: number = parseInt(data.grade_a_completed);
          const gradeACal: number = gradeACount5Percent - gradeACompleted;
          const gradeACount: number = gradeACal >= 0 ? gradeACal : 0;
          const gradeOthersCount: number = parseInt(data.grade_others);
          const gradeScrapCount: number = parseInt(data.grade_scrap);

          totalItem =
            totalItem + gradeACount + gradeOthersCount + gradeScrapCount;
        }
      });
    }

    sqlSelectStatement = sqlSelectStatement.trim();
    sqlSelectStatement = sqlSelectStatement.substring(
      0,
      sqlSelectStatement.length - 1,
    );

    // construct query
    const query = `
    SELECT
      ${sqlSelectStatement}
      FROM core.job;
    `;

    const queryData = await this.jobsRepo.query(query);
    let results = {};

    // processing data after query

    if (hasViewInspectionAllPermission) {
      results = {
        ...results,
        totalInspectionJob: totalItem,
      };
    }

    if (hasViewInspectionMyPermission) {
      results = {
        ...results,
        myInspection: parseInt(queryData[0].my_inspection),
      };
    }

    if (hasViewRepairPendingPermission) {
      results = {
        ...results,
        totalRepairJob: parseInt(queryData[0].repair),
      };
    }

    if (hasViewRepairMyPermission) {
      results = {
        ...results,
        myRepair: parseInt(queryData[0].my_repair),
      };
    }

    return results;
  }

  async getCountByType(context: Request, user: WithUserContext, type: string) {
    const userKey = user.userKey;
    const yesterday = getDateFromToday(-1);

    if (type === 'inspection') {
      const searchDate = context.query.inspectionDate as string;
      const isMyJob = context.query.isMyInspectionJob === 'true';

      const queryDate = searchDate
        ? new Date(searchDate)
        : new Date(yesterday[3]);

      const strQueryDate = `${queryDate.getFullYear()}-${(
        queryDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}-${queryDate.getDate().toString().padStart(2, '0')}`;

      if (isMyJob) {
        const query = `
        SELECT 
          count(
            CASE WHEN (
              status IN ('75_INSPECTION_ASSIGNED') 
              AND current_grade = 'A' 
              AND inspected_by = '${userKey}'
            ) THEN 1 END
          ) AS grade_a, 
          count(
            CASE WHEN (
              status IN ('75_INSPECTION_ASSIGNED')
              AND current_grade != 'A' 
              AND qc_status != 'scrap'
              AND inspected_by = '${userKey}'
            ) THEN 1 END
          ) AS grade_others, 
          count(
            CASE WHEN (
              status IN ('75_INSPECTION_ASSIGNED')
              AND qc_status = 'scrap'
              AND inspected_by = '${userKey}'
            ) THEN 1 END
          ) AS grade_scrap 
        FROM 
          core.job
    
        `;

        const result = await this.jobsRepo.query(query);
        const gradeA: number = parseInt(result[0].grade_a);
        const gradeOthersCount: number = parseInt(result[0].grade_others);
        const gradeScrapCount: number = parseInt(result[0].grade_scrap);

        const totalItem = gradeA + gradeOthersCount + gradeScrapCount;

        return {
          total: totalItem,
          gradeATotal: 0,
          gradeACompleted: 0,
          gradeA: gradeA,
          gradeOthers: gradeOthersCount,
          gradeScrap: gradeScrapCount,
        };
      } else {
        const query = `
        SELECT 
          count(
            CASE WHEN (
              status IN (
                '70_REPAIR_COMPLETED', '75_INSPECTION_ASSIGNED', 
                '80_INSPECTION_COMPLETED') 
              AND current_grade = 'A' 
              AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = '${strQueryDate}'
            ) THEN 1 END
          ) AS grade_a_all, 
          count(
            CASE WHEN (
              status IN ('80_INSPECTION_COMPLETED') 
              AND current_grade = 'A'
              AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = '${strQueryDate}'
            ) THEN 1 END
          ) AS grade_a_completed, 
          count(
            CASE WHEN (
              status IN ('70_REPAIR_COMPLETED', '75_INSPECTION_ASSIGNED')
              AND current_grade != 'A' 
              AND qc_status != 'scrap'
              AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = '${strQueryDate}'
            ) THEN 1 END
          ) AS grade_others, 
          count(
            CASE WHEN (
              status IN ('60_QC_COMPLETED', '65_REPAIR_ASSIGNED', '75_INSPECTION_ASSIGNED')
              AND qc_status = 'scrap'
              AND date_trunc('day', repaired_at at time zone 'Asia/Bangkok') = '${strQueryDate}'
            ) THEN 1 END
          ) AS grade_scrap 
        FROM 
          core.job
    
        `;

        const result = await this.jobsRepo.query(query);
        const gradeATotal: number = parseInt(result[0].grade_a_all);
        const gradeACount5Percent: number = Math.ceil(gradeATotal * 0.05);
        const gradeACompleted: number = parseInt(result[0].grade_a_completed);
        const gradeOthersCount: number = parseInt(result[0].grade_others);
        const gradeScrapCount: number = parseInt(result[0].grade_scrap);

        const gradeACal = gradeACount5Percent - gradeACompleted;
        const gradeA = gradeACal > 0 ? gradeACal : 0;
        const totalItem = gradeA + gradeOthersCount + gradeScrapCount;

        return {
          total: totalItem,
          gradeATotal: gradeATotal,
          gradeACompleted: gradeACompleted,
          gradeA: gradeA,
          gradeOthers: gradeOthersCount,
          gradeScrap: gradeScrapCount,
        };
      }
    }

    if (type === 'repair') {
      const isMyJob = context.query.isMyRepairJob === 'true';
      if (isMyJob) {
        const query = `
        SELECT
          COUNT(
            CASE WHEN (
              (STATUS = '65_REPAIR_ASSIGNED' AND QC_STATUS IN ('fix', 'refurbish')) AND REPAIRED_BY = '${userKey}'
            ) THEN 1 END
          ) AS MY_REPAIR_TOTAL, 
          COUNT(
            CASE WHEN (
              (STATUS = '65_REPAIR_ASSIGNED' AND QC_STATUS = 'fix') AND REPAIRED_BY = '${userKey}'
            ) THEN 1 END
          ) AS MY_REPAIR_FIX, 
          COUNT(
            CASE WHEN (
              (STATUS = '65_REPAIR_ASSIGNED' AND QC_STATUS = 'refurbish' AND REPAIR_LIST_VALUE IS NULL) AND REPAIRED_BY = '${userKey}'
            ) THEN 1 END
          ) AS MY_REPAIR_REFURBISH_FROM_QC, 
          COUNT(
            CASE WHEN (
              (STATUS = '65_REPAIR_ASSIGNED' AND QC_STATUS = 'refurbish' AND REPAIR_LIST_VALUE IS NOT NULL) AND REPAIRED_BY = '${userKey}'
            ) THEN 1 END
          ) AS MY_REPAIR_REFURBISH_FROM_FIX
          FROM 
            core.job
          `;

        const result = await this.jobsRepo.query(query);

        return {
          myRepairTotal: parseInt(result[0].my_repair_total),
          myRepairFix: parseInt(result[0].my_repair_fix),
          myRepairRefurbishFromQC: parseInt(
            result[0].my_repair_refurbish_from_qc,
          ),
          myRepairRefurbishFromFix: parseInt(
            result[0].my_repair_refurbish_from_fix,
          ),
        };
      } else {
        const query = `
        SELECT
          COUNT(
            CASE WHEN (
              (
                STATUS = '60_QC_COMPLETED' 
                AND QC_STATUS IN ('fix', 'refurbish')
              ) 
              OR (
                STATUS = '60_QC_COMPLETED' 
                AND QC_STATUS = 'fix'
              ) 
              OR (
                STATUS = '60_QC_COMPLETED' 
                AND QC_STATUS = 'refurbish'
              ) 
              OR (STATUS = '79_INSPECTION_FAILED')
            ) THEN 1 END
          ) AS REPAIR_TOTAL, 
          COUNT(
            CASE WHEN (
              STATUS = '60_QC_COMPLETED' 
              AND QC_STATUS = 'fix'
            ) THEN 1 END
          ) AS FIX, 
          COUNT(
            CASE WHEN (
              STATUS = '60_QC_COMPLETED' 
              AND QC_STATUS = 'refurbish'
            ) THEN 1 END
          ) AS REFURBISH, 
          COUNT(
            CASE WHEN (STATUS = '79_INSPECTION_FAILED') THEN 1 END
          ) AS INSPECTION_FAILED
          FROM 
            core.job
          `;

        const result = await this.jobsRepo.query(query);

        return {
          total: parseInt(result[0].repair_total),
          fix: parseInt(result[0].fix),
          refurbish: parseInt(result[0].refurbish),
          inspectionFailed: parseInt(result[0].inspection_failed),
        };
      }
    }

    return null;
  }

  async exportJobs(
    body: ExportJobsDto | ExportJobsProductDto,
    user: WithUserContext,
    jobData: JobEntity[],
    exportType: string = 'REPORT',
    queryStream?: SelectQueryBuilder<JobEntity>,
    res?: Response,
  ) {
    const filterDataByHeader: any[] = [];
    let headerSlugList: IExcelTableSlugAllJobStatus[] = [];

    if (exportType !== JobExportType.REPORT) {
      headerSlugList = headerList[exportType] as IExcelTableSlugAllJobStatus[];
    } else {
      headerSlugList = body.headerSlug as IExcelTableSlugAllJobStatus[];
    }

    const filterParam = body.filter;
    const companyId = user.company;
    const users = await this.adminUserService.getAllUsers(companyId);
    const userMappedTable = users.reduce((key, user) => {
      key[user.userKey] = user.name;
      return key;
    }, {}) as {};

    const branches = await this.branchRepo.find({
      where: {
        companyId: companyId,
      },
    });

    const branchMappedTable = branches.reduce((key, branch) => {
      key[branch.branchId] = `${branch.branchId} ${branch.title}`;
      return key;
    }, {}) as {};

    // generate filename from current date
    const [currentYear, currentMonth, dateToday] = getDateFromToday();
    const fileName = `${exportFileName[exportType]}_${dateToday
      .toString()
      .padStart(2, '0')}${currentMonth
      .toString()
      .padStart(2, '0')}${currentYear}`;

    // convert header for excel
    const headers = convertSlugToHeaderColumn({
      slugList: headerSlugList,
      tableType: 'ALL_STATUS_JOB',
    });

    // remove header key that are not whitelist member
    for (const key in headers) {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    }

    // custom sub header text
    if (exportType === 'PRODUCT') {
      if (headers.inspectedAt) {
        headers.inspectedAt.subHeader = 'วันที่ตรวจสินค้าผ่าน';
      }
      if (headers.status) {
        headers.status.subHeader = 'ประเภทสินค้า';
      }
    } else {
      if (headers.inspectedAt) {
        headers.inspectedAt.subHeader = 'วันเวลาที่ Inspection';
      }
      if (headers.status) {
        headers.status.subHeader = 'สถานะ';
      }
    }

    // flatten and mapping data
    jobData.forEach((job) => {
      //map phone model value
      for (let key in job['r_model_identifiers']) {
        job[key] = job['r_model_identifiers'][key];
      }

      // loop map user
      for (const item of jobUserColumn) {
        job[item.entityKey] = userMappedTable[job[item.dbKey] ?? ''];
      }

      // loop map time & convert UTC to ICT
      for (const item of jobDateTimeColumn) {
        job[item.entityKey] =
          job[item.dbKey] && this.getICTDateTime(job[item.dbKey]);
      }

      // loop map remaining columns
      for (const item of jobMapDatColumn) {
        job[item.entityKey] = job[item.dbKey];
      }

      // map branch
      job.branchId = branchMappedTable[job['r_branch_id'] ?? ''];

      // map price value
      job.suggestedPrice =
        Number(job['r_suggested_price']) === 0
          ? undefined
          : job['r_suggested_price'];
      job.purchasedPrice =
        Number(job['r_purchased_price']) === 0
          ? undefined
          : job['r_purchased_price'];
    });

    // deep copy value to support qcstatus data
    const slugFilter = Object.values(headers).map((header) => header.keyName);

    if (!slugFilter.includes('qcStatus')) {
      slugFilter.push('qcStatus');
    }

    // filter data from given header
    jobData.forEach((data) => {
      const filteredObject = Object.keys(data)
        .filter((key) => slugFilter.includes(key))
        .reduce((obj, key) => {
          obj[key] = data[key];
          return obj;
        }, {});
      filterDataByHeader.push(filteredObject);
    });

    filterDataByHeader.forEach((data) => {
      if (data.status === JobStatus.INSPECTION_AUTO_COMPLETED) {
        data.inspectedBy = 'ระบบตรวจสอบอัตโนมัติ';
      }
      if (exportType === JobExportType.REPORT) {
        data.status = getJobsStatusFromCodeStatus(data.status, data.qcStatus);

        data.shippingStatus = shippingStatusCodeMapping[data.shippingStatus];

        data.qcStatus = qcStatusCodeMapping[data.qcStatus];
      } else if (exportType === JobExportType.PRODUCT) {
        data.status = getProductStatusFromCodeStatus(
          data.status,
          data.qcStatus,
        );
      }
    });

    let headerCondition = {};
    if (filterParam) {
      filterParam['sumOfRecords'] = filterDataByHeader.length;
      // validate and contruct header filter
      if (filterParam.hasOwnProperty('branch') && jobData.length > 0) {
        filterParam['branch'] = `${jobData[0].branchId}`;
      }

      filterParam['exportType'] = exportType;
      headerCondition = this.validateHeaderCondition(filterParam, exportType);
    }

    // generate excel file
    this.excelManagerService.options = { headers: headers };

    const excelFileBuffer = this.excelManagerService.generateExcelFile(
      filterDataByHeader,
      fileName,
      headerCondition,
    );

    // save export activity
    const saveExportActivity = new ExportActivitiesEntity();
    saveExportActivity.companyId = user.company;
    saveExportActivity.condition = {
      filter: filterParam ?? {},
      columnSlug: headerSlugList ?? [],
    };
    saveExportActivity.downloadedBy = user.userKey;
    this.exportActivitiesRepo.save(saveExportActivity);

    return excelFileBuffer;
  }

  async exportJobsStream(
    body: ExportJobsDto | ExportJobsProductDto | ExportSapDto,
    user: WithUserContext,
    jobData: JobEntity[],
    exportType: string = 'REPORT',
    queryStream?: SelectQueryBuilder<JobEntity>,
    res?: Response,
  ): Promise<Stream> {
    const filterDataByHeader: any[] = [];
    let headerSlugList: IExcelTableSlugAllJobStatus[] = [];

    if (exportType !== JobExportType.REPORT) {
      headerSlugList = headerList[exportType] as IExcelTableSlugAllJobStatus[];
    } else {
      headerSlugList = body.headerSlug as IExcelTableSlugAllJobStatus[];
    }

    const filterParam = body.filter;
    const companyId = user.company;
    const users = await this.adminUserService.getAllUsers(companyId);
    const userMappedTable = users.reduce((key, user) => {
      key[user.userKey] = user.name;
      return key;
    }, {}) as {};

    const branches = await this.branchRepo.find({
      where: {
        companyId: companyId,
      },
    });

    const branchMappedTable = branches.reduce((key, branch) => {
      key[branch.branchId] = `${branch.branchId} ${branch.title}`;
      return key;
    }, {}) as {};

    // generate filename from current date
    const [currentYear, currentMonth, dateToday] = getDateFromToday();
    const fileName = `${exportFileName[exportType]}_${dateToday
      .toString()
      .padStart(2, '0')}${currentMonth
      .toString()
      .padStart(2, '0')}${currentYear}`;

    // convert header for excel
    const headers = convertSlugToHeaderColumn({
      slugList: headerSlugList,
      tableType: 'ALL_STATUS_JOB',
    });

    // remove header key that are not whitelist member
    for (const key in headers) {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    }

    // custom sub header text
    if (exportType === 'PRODUCT') {
      if (headers.inspectedAt) {
        headers.inspectedAt.subHeader = 'วันที่ตรวจสินค้าผ่าน';
      }
      if (headers.status) {
        headers.status.subHeader = 'ประเภทสินค้า';
      }
    } else {
      if (headers.inspectedAt) {
        headers.inspectedAt.subHeader = 'วันเวลาที่ Inspection';
      }
      if (headers.status) {
        headers.status.subHeader = 'สถานะ';
      }
    }

    // deep copy value to support qcstatus data
    const slugFilter = Object.values(headers).map((header) => header.keyName);

    if (!slugFilter.includes('qcStatus')) {
      slugFilter.push('qcStatus');
    }

    let headerCondition = {};

    if (filterParam) {
      // validate and contruct header filter
      if (filterParam.hasOwnProperty('branch')) {
        filterParam['branch'] = branchMappedTable[filterParam['branch']] ?? '-';
      }

      filterParam['exportType'] = exportType;
      headerCondition = this.validateHeaderCondition(filterParam, exportType);
    }

    // generate excel file
    this.excelManagerService.options = { headers: headers };
    let excelFileBuffer: Promise<Stream>;
    if (queryStream) {
      excelFileBuffer = this.excelManagerService.generateExcelFileWithStream(
        filterDataByHeader,
        exportType !== JobExportType.SAP ? fileName : 'SAP_REPORT',
        queryStream,
        {
          userMappedTable: userMappedTable,
          branchMappedTable: branchMappedTable,
        },
        exportType,
        headerCondition,
      );

      await this.saveActivityLog(user, filterParam, headerSlugList);

      return excelFileBuffer;
    }

    await this.saveActivityLog(user, filterParam, headerSlugList);
    return new Stream();
  }

  async saveActivityLog(
    user: WithUserContext,
    filterParam?: any,
    headerSlugList?: IExcelTableSlugAllJobStatus[],
  ) {
    // save export activity
    const saveExportActivity = new ExportActivitiesEntity();
    saveExportActivity.companyId = user.company;
    saveExportActivity.condition = {
      filter: filterParam ?? {},
      columnSlug: headerSlugList ?? [],
    };
    saveExportActivity.downloadedBy = user.userKey;
    this.exportActivitiesRepo.save(saveExportActivity);
  }

  validateHeaderCondition(filter: {}, exportType: string = 'REPORT') {
    // construct header condition
    const headerCondition = {};

    if (exportType === JobExportType.SAP) {
      return {};
    }

    // common header
    headerCondition['jobId'] = `Transaction ID: ${filter['jobId'] ?? '-'}`;
    headerCondition['deviceKey'] = `เลข IMEI: ${filter['deviceKey'] ?? '-'}`;
    headerCondition['brand'] = `ยี่ห้อ: ${filter['brand'] ?? '-'}`;
    headerCondition['model'] = `รุ่น: ${filter['model'] ?? '-'}`;

    if (exportType === JobExportType.REPORT) {
      headerCondition['status'] = `สถานะ: ${filter['status'] ?? '-'}`;
      headerCondition['currentGrade'] = `เกรด: ${
        filter['currentGrade'] ?? '-'
      }`;
      headerCondition['branch'] = `สาขาที่รับซื้อ: ${filter['branch'] ?? '-'}`;
      headerCondition['updatedDate'] = this.getDateRangeCondition(
        filter['minUpdatedDate'],
        filter['maxUpdatedDate'],
      );

      headerCondition['priceRange'] = this.getPriceRangeCondition(
        filter['minPrice'],
        filter['maxPrice'],
      );
    } else if (exportType === JobExportType.PRODUCT) {
      const status = filter['qcStatusFromTab'];
      headerCondition['currentGrade'] = `เกรด: ${
        filter['currentGrade'] ?? '-'
      }`;
      if (status === 'PRODUCT_READY') {
        headerCondition['status'] = `ประเภท: ${ProductStatus.READY}`;
      } else if (status === 'PRODUCT_SCRAP') {
        headerCondition['status'] = `ประเภท: ${ProductStatus.SCARP}`;
      } else {
        headerCondition['status'] = `ประเภท: -`;
      }
    }

    headerCondition['sumOfRecords'] =
      `Sum of records: ${filter['sumOfRecords']}`;

    return headerCondition;
  }

  getDateRangeCondition(
    minUpdatedDateParam: string | undefined,
    maxUpdatedDateParam: string | undefined,
  ) {
    const minUpdatedDate = minUpdatedDateParam
      ? this.validateAndReturnDate(minUpdatedDateParam, 'DDMMYYYY')
      : undefined;
    const maxUpdatedDate = maxUpdatedDateParam
      ? this.validateAndReturnDate(maxUpdatedDateParam, 'DDMMYYYY')
      : undefined;

    let dateRange = '-';

    if (minUpdatedDate && !maxUpdatedDate) {
      dateRange = `ตั้งแต่วันที่ ${minUpdatedDate} ขึ้นไป`;
    } else if (!minUpdatedDate && maxUpdatedDate) {
      dateRange = `ไม่เกินวันที่ ${maxUpdatedDate}`;
    } else if (minUpdatedDate && maxUpdatedDate) {
      dateRange = `${minUpdatedDate} - ${maxUpdatedDate}`;
    }

    return `วันที่อัปเดตล่าสุด: ${dateRange}`;
  }

  getPriceRangeCondition(
    minPurchasedPrice: string | undefined,
    maxPurchasedPrice: string | undefined,
  ) {
    let priceRange = '-';
    if (minPurchasedPrice && !maxPurchasedPrice) {
      priceRange = `ตั้งแต่ ${minPurchasedPrice} ขึ้นไป`;
    } else if (!minPurchasedPrice && maxPurchasedPrice) {
      priceRange = `ไม่เกิน ${maxPurchasedPrice}`;
    } else if (minPurchasedPrice && maxPurchasedPrice) {
      priceRange = `${minPurchasedPrice} - ${maxPurchasedPrice}`;
    }

    return `ช่วงราคารับซื้อ: ${priceRange}`;
  }

  getICTDateTime(utcDate: Date) {
    const ictOffsetTime = 7 * 60 * 60 * 1000;
    const ictDate = new Date(utcDate.getTime() + ictOffsetTime);

    return ictDate;
  }

  async updateJobProduct(
    jobId: string,
    body: UpdateJobProductDto,
    user: WithUserContext,
  ) {
    const job = await this.jobsRepo.findOneBy({
      jobId: jobId,
    });

    if (!job) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    if (
      job.status !== JobStatus.INSPECTION_COMPLETED &&
      job.status !== JobStatus.INSPECTION_AUTO_COMPLETED
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid job status - the status must be: ${JobStatus.INSPECTION_COMPLETED}, ${JobStatus.INSPECTION_AUTO_COMPLETED}`,
      );
    }

    if (job.isConfirmPrice) {
      throw this.baseExceptionService.exception('INVALID_JOB_FOR_ACTION');
    }

    if (job.costPrice) {
      const retailMargin = getMargin(job.costPrice, body.retailPrice);
      const wholeSaleMargin = getMargin(job.costPrice, body.wholeSalePrice);

      job.retailPrice = body.retailPrice;
      job.retailMargin = retailMargin.percent;
      job.marginRetailBaht = retailMargin.baht;

      job.wholeSalePrice = body.wholeSalePrice;
      job.wholeSaleMargin = wholeSaleMargin.percent;
      job.marginWholeSaleBaht = wholeSaleMargin.baht;

      // stamp history
      const history: UpdateCostHx[] = [];

      if (job.adminUpdateCostListValue) {
        history.push(...job.adminUpdateCostListValue);
      }

      history.push({
        at: new Date(),
        by: { key: user.userKey, name: user.name ?? '' },
        retailPrice: body.retailPrice,
        wholeSalePrice: body.wholeSalePrice,
      });

      job.adminUpdateCostListValue = history;

      await this.jobsRepo.save(job);
    } else {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `There is no costPrice for this job.`,
      );
    }

    return null;
  }

  async setOperationCost(value: any, companyId: string): Promise<any> {
    const result = await this.systemConfigService.setSystemConfig(
      companyId,
      'operation_cost',
      value,
    );
    if (result) {
      await reCalculateProduct(result, this.queue, this.jobsRepo);
    }
    return result;
  }

  async setStandardConfig(
    configType: string,
    configValue: Record<string, any>,
    user: WithUserContext,
  ): Promise<any> {
    const companyId = user.company;
    let configKey = '';
    let isKeyValid = false;

    if (configType === ConfigType.MARKETING) {
      const keyToValidate = ['marketing'];
      configKey = ConfigType.MARKETING;
      isKeyValid = this.validateConfigKey(keyToValidate, configValue);
    } else if (configType === ConfigType.OPERATION_COST) {
      const keyToValidate = ['logistic', 'warehouseRental', 'productPackaging'];
      configKey = ConfigType.OPERATION_COST;
      isKeyValid = this.validateConfigKey(keyToValidate, configValue);
    } else {
      // return error key type not match
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `No configType: ${configType} in systemConfig`,
      );
    }

    // return error after validate configValue key
    if (!isKeyValid) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Missing configValue key`,
      );
    }

    // get old data
    const configData = await this.systemConfigService.getSystemConfig(
      companyId,
      configKey,
    );

    // set new data
    for (const key in configData) {
      if (configData.hasOwnProperty(key) && configValue.hasOwnProperty(key)) {
        configData[key] = configValue[key];
      }
    }

    // save data
    const result = await this.systemConfigService.setSystemConfig(
      companyId,
      configKey,
      configData,
    );

    if (result) {
      // save activity
      const configActivity = new ConfigActivitiesEntity();
      configActivity.companyId = companyId;
      configActivity.configBy = user.userKey;
      configActivity.configType = configType;
      configActivity.configValue = configData;
      configActivity.configAt = new Date();
      await this.configActivityRepo.save(configActivity);

      // recal job
      await reCalculateProduct(result, this.queue, this.jobsRepo);
    }
    return configData;
  }

  validateConfigKey(keyToValidate: string[], configValue: Record<string, any>) {
    return keyToValidate.every((key) => configValue.hasOwnProperty(key));
  }

  async confirmPrice(body: ConfirmPriceDto, user: WithUserContext) {
    //set key map jobId and job updatedAt from job body
    const jobBodyObjectMap = new Map();
    body.jobs.forEach((job) => {
      jobBodyObjectMap.set(job.jobId, job.updatedAt);
    });
    //set array jobId from job body
    const jobIdBodyList = body.jobs.map((item) => item.jobId);

    const errorRespData: any = {
      updatedJobs: [],
      confirmedJobs: [],
      notFoundJobs: [],
    };

    const confirmJobs = await this.jobsRepo.find({
      where: {
        jobId: In(body.jobs.map((item) => item.jobId)),
        status: In([
          JobStatus.INSPECTION_COMPLETED,
          JobStatus.INSPECTION_AUTO_COMPLETED,
        ]),
      },
    });

    const confirmJobIdList = confirmJobs.map((item) => item.jobId);

    //find job not found from compare list body and list query
    const itemDiff = jobIdBodyList.filter(
      (item) => !confirmJobIdList.includes(item),
    );
    if (itemDiff.length !== 0) {
      errorRespData.notFoundJobs = itemDiff;
    }
    // set confirm to true for every jobs
    confirmJobs.forEach((job) => {
      if (job.isConfirmPrice) {
        errorRespData.confirmedJobs.push(job.jobId);
      } else {
        if (job.updatedAt?.toISOString() !== jobBodyObjectMap.get(job.jobId)) {
          errorRespData.updatedJobs.push({
            jobId: job.jobId,
            updatedAt: job.updatedAt,
          });
        } else {
          job.isConfirmPrice = true;
          job.confirmedPriceBy = user.userKey;
          job.confirmedPriceAt = DateTime.now().toJSDate();
        }
      }
    });
    if (
      errorRespData.updatedJobs.length !== 0 ||
      errorRespData.confirmedJobs.length !== 0 ||
      errorRespData.notFoundJobs.length !== 0
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_JOB_FOR_ACTION',
        errorRespData,
      );
    }

    await this.jobsRepo.save(confirmJobs);
    return null;
  }

  async updateIncompleteShipping(
    jobId: string,
    body: RejectAOBody,
    user: WithUserContext,
  ) {
    const remark = body.remark;

    const queryJobEntity = await this.jobsRepo.findOne({
      where: { jobId },
      relations: { allocationOrder: true },
    });

    //validate data
    const jobEntity = this.validateAOJobs(queryJobEntity);

    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    const incompleteAOListValue = {
      allocationOrderId: jobEntity.allocationOrderId,
      userKey: user.userKey,
      userName: user.name,
      remark: remark,
      type: AOShippingStatus.NOT_SHIPPED,
      confirmedAt: new Date(),
    } as AOListValue;

    jobEntity.incompleteAOListValue = jobEntity.incompleteAOListValue ?? [];
    jobEntity.incompleteAOListValue.push(incompleteAOListValue);

    jobEntity.status =
      jobEntity.inspectedBy === null
        ? JobStatus.INSPECTION_AUTO_COMPLETED
        : JobStatus.INSPECTION_COMPLETED;

    jobEntity.aoShippingStatus = AOShippingStatus.NOT_SHIPPED;

    const jobsSnapshot = prepareJobsSnapshot({
      job: jobEntity,
      user: user,
      aoIncompleteInspectedAt: new Date(),
      remark: remark,
    });

    jobEntity.aoShippingStatus = null;

    const aoEntity = this.addOrReplaceJobsSnapShotData(
      jobEntity,
      jobEntity.allocationOrder!,
      jobsSnapshot,
    );

    jobEntity.allocationOrderId = null;
    jobEntity.allocationOrder = undefined;

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();
      await queryRunner.manager.save(jobEntity);
      await this.allocationOrderRepo.save(aoEntity);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return null;
  }

  async updateIncompleteReceiving(
    jobId: string,
    body: RejectAOBody,
    user: WithUserContext,
  ) {
    const remark = body.remark;

    const queryJobEntity = await this.jobsRepo.findOne({
      where: { jobId },
      relations: { allocationOrder: true },
    });

    //validate data
    const jobEntity = this.validateAOJobs(queryJobEntity);

    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    const incompleteAOListValue = {
      allocationOrderId: jobEntity.allocationOrderId,
      userKey: user.userKey,
      userName: user.name,
      remark: remark,
      type: AOShippingStatus.NOT_SCANNED,
      confirmedAt: new Date(),
    } as AOListValue;

    jobEntity.incompleteAOListValue = jobEntity.incompleteAOListValue ?? [];
    jobEntity.incompleteAOListValue.push(incompleteAOListValue);
    jobEntity.aoShippingStatus = AOShippingStatus.NOT_SCANNED;

    const jobsSnapshot = prepareJobsSnapshot({
      job: jobEntity,
      user: user,
      aoIncompleteInspectedAt: new Date(),
      remark: remark,
    });

    jobEntity.aoShippingStatus = AOShippingStatus.SHIPPED;

    const aoEntity = this.addOrReplaceJobsSnapShotData(
      jobEntity,
      jobEntity.allocationOrder!,
      jobsSnapshot,
    );

    //create new ao
    const aoDraftId = await this.allocationOrderService.createDraft(user);
    const newAOEntity = await this.allocationOrderRepo.findOne({
      where: { allocationOrderId: aoDraftId },
    });

    if (!newAOEntity) {
      throw this.baseExceptionService.exception('INVALID_ALLOCATION_DRAFT');
    }

    newAOEntity.status = AllocationOrderStatus.IN_TRANSIT;
    newAOEntity.fromBranchId = aoEntity.fromBranchId;
    newAOEntity.toBranchId = aoEntity.toBranchId;
    newAOEntity.quantity = 1;
    newAOEntity.createdBy = null;
    jobEntity.allocationOrder = newAOEntity;

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      await queryRunner.manager.save(jobEntity);
      await queryRunner.manager.save(aoEntity);
      await queryRunner.manager.save(newAOEntity);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return newAOEntity;
  }

  async updateIncompleteLost(
    jobId: string,
    body: LostAOBody,
    user: WithUserContext,
  ) {
    const remark = body.remark;
    const videoPath = body.videoPath;

    const queryJobEntity = await this.jobsRepo.findOne({
      where: { jobId },
      relations: { allocationOrder: true },
    });

    //validate data
    const jobEntity = this.validateAOJobs(queryJobEntity);

    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    const incompleteAOListValue = {
      allocationOrderId: jobEntity?.allocationOrderId,
      userKey: user.userKey,
      userName: user.name,
      remark: remark,
      type: AOShippingStatus.LOST,
      videoPath: videoPath,
      confirmedAt: new Date(),
    } as AOListValue;

    jobEntity.incompleteAOListValue = jobEntity.incompleteAOListValue ?? [];
    jobEntity.incompleteAOListValue.push(incompleteAOListValue);
    jobEntity.aoShippingStatus = AOShippingStatus.LOST;

    const jobsSnapshot = prepareJobsSnapshot({
      job: jobEntity,
      user: user,
      aoIncompleteInspectedAt: new Date(),
      remark: remark,
      videoPath: videoPath,
    });

    const aoEntity = this.addOrReplaceJobsSnapShotData(
      jobEntity,
      jobEntity.allocationOrder!,
      jobsSnapshot,
    );

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      await queryRunner.manager.save(jobEntity);
      await queryRunner.manager.save(aoEntity);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return aoEntity;
  }

  validateAOJobs(jobEntity: JobEntity | null) {
    const validAOStatusList = [
      AllocationOrderStatus.PARTIAL_RECEIVED,
      AllocationOrderStatus.REJECT_BY_SHOP,
    ];

    if (!jobEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Transaction ID not found',
      );
    }

    if (!jobEntity.allocationOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Job is not in this allocation order',
      );
    }

    if (!validAOStatusList.includes(jobEntity.allocationOrder.status)) {
      throw this.baseExceptionService.exception('INVALID_AO_STATUS_TO_CONFIRM');
    }

    if (jobEntity.aoShippingStatus !== AOShippingStatus.SHIPPED) {
      throw this.baseExceptionService.exception('INVALID_INPUT_FOR_UPDATE_JOB');
    }

    return jobEntity;
  }

  addOrReplaceJobsSnapShotData(
    jobEntity: JobEntity,
    aoEntity: AllocationOrderEntity,
    jobsSnapshot,
  ) {
    aoEntity.isInspectedByAdmin = true;

    if (aoEntity.jobsSnapshot) {
      const index = aoEntity.jobsSnapshot.findIndex(
        (item) => item.jobId === jobEntity.jobId,
      );

      if (index !== -1) {
        aoEntity.jobsSnapshot[index] = jobsSnapshot;
      } else {
        aoEntity.jobsSnapshot.push(jobsSnapshot);
      }
    } else {
      aoEntity.jobsSnapshot = [jobsSnapshot];
    }

    return aoEntity;
  }

  async getPresigned(jobId: string, key: string, user: WithUserContext) {
    const keyPath = getS3JobUrlPath(user.company, jobId, key);
    const url = await this.s3Service.getUploadFilePreSignedUrl(keyPath);
    return { url: url, path: keyPath };
  }
}
