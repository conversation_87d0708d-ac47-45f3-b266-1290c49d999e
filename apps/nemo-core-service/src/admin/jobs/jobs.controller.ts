import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  Res,
  Param,
  Patch,
  StreamableFile,
} from '@nestjs/common';
import type { Response } from 'express';
import { Readable } from 'stream';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { JobEntity, JobActivitiesType } from '../../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import {
  GetJobsDto,
  SuggestPriceDto,
  CommentJobDto,
  GetReceiveJobsDto,
  RepairConfirmDto,
  QCStatusDto,
  InspectJobDto,
  AssignRepairDto,
  ExportJobsDto,
  UpdateJobProductDto,
  ExportJobsProductDto,
  SetOperationCostDto,
  SetStandardConfigDto,
  ConfirmPriceDto,
  RejectAODto,
  LostAODto,
} from './dto';
import { Request } from 'express';
import { WithUserContext } from '../../interfaces';
import { AdminJobsService } from './jobs.service';
import { JobsService as ShopJobsService } from '../../shop/jobs/jobs.service';
import {
  CrudRemoveRoute,
  Roles,
  WithUser,
  Permissions,
} from '../../decorators';
import { DataChangeSlug, Summary } from '../../subscriber';
import {
  mappingUrlWithCompanyId,
  Role,
  Permission,
  PermissionAction,
  permissionCMSGetJobList,
  permissionCMSGetJobId,
  permissionCMSGetCountAndMenuCount,
} from '../../config';
import { GetMediaUrlDto } from '../../../src/shop/jobs/dto/get-media-url';
import { ExportSapDto } from './dto/export-sap.dto';

@CrudRemoveRoute(['removeOne', 'softRemoveOne', 'restore'])
@Controller('v1/admin/jobs')
export class JobsController extends CrudController<JobEntity> {
  constructor(
    @InjectRepository(JobEntity) repo: Repository<JobEntity>,
    private readonly jobsService: AdminJobsService,
    private readonly shopJobsService: ShopJobsService,
  ) {
    super(JobEntity, 'job', repo, {
      resourceKeyPath: 'jobId',
      order: { updatedAt: 'asc' },
      defaultPopulate: () => {
        return [
          'deliveryOrder',
          'branch',
          'adminUser',
          'qcUser',
          'repairedUser',
          'inspectedUser',
          'updatedUser',
          'createdUser',
          'modelMaster',
          'color',
          'allocationOrder',
        ];
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<JobEntity>,
      ) => await this.jobsService.buildSearchQuery(request, listQuery),
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<JobEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      sanitizeInputBody: async (
        _ctx,
        _man,
        data: Partial<JobEntity>,
        isCreated,
      ): Promise<Partial<JobEntity>> =>
        this.jobsService.sanitizeInputBody(data, isCreated),
      computeUpdatePayload: async (
        _ctx,
        _man,
        raw: JobEntity,
        data,
      ): Promise<Partial<JobEntity>> =>
        this.jobsService.computeUpdatePayload(raw, data),
      preSave: [
        async (_ctx, _man, data: any, isCreated): Promise<any> =>
          this.jobsService.preSave(_man, data, isCreated),
      ],
    });
  }

  @Get()
  @Permissions(permissionCMSGetJobList)
  async getJobs(@Req() context: Request, @Query() query: GetJobsDto) {
    const orderBy = query?.orderBy;
    if (orderBy) {
      const field = [
        'brand',
        'model',
        'rom',
        'branchName',
        'adminUserKey',
        'awbNumber',
        'aoShippingStatus',
        'aoAwbNumber',
      ].find((s) => orderBy.includes(s));
      if (field) {
        if (orderBy.includes('branchName')) {
          query.orderBy = orderBy.replace(field, `rel:branch title`);
        } else if (orderBy.includes('adminUserKey')) {
          query.orderBy = orderBy.replace(field, `rel:adminUser name`);
        } else if (orderBy.includes('awbNumber')) {
          query.orderBy = orderBy.replace(field, `rel:deliveryOrder awbNumber`);
        } else if (orderBy.includes('aoAwbNumber')) {
          query.orderBy = orderBy.replace(
            field,
            `rel:allocationOrder awbNumber`,
          );
        } else if (orderBy.includes('aoShippingStatus')) {
          query.orderBy = orderBy.replace(
            field,
            `aoShippingStatus asc,allocationOrderId`,
          );
        } else {
          query.orderBy = orderBy.replace(field, `modelIdentifiers ${field}`);
        }
      }
      if (!orderBy.includes('jobId') && !orderBy.includes('aoShippingStatus')) {
        query.orderBy = query.orderBy + ',jobId asc';
      }
    }

    return super.findAll(context, query);
  }

  @Get('/:id/with-relation')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.VIEW])
  async getJobWithRelation(
    @Param('id') id: string,
    @Query('relations') relations: string[],
    @WithUser() user: WithUserContext,
  ) {
    const { company } = user;
    let job = await this.shopJobsService.getJobWithRelation({
      jobId: id,
      relations: relations ?? [],
      company,
    });
    job = await this.shopJobsService.getMediaUrl(job);
    return job;
  }

  @Patch('/receive-jobs')
  @Permissions([Permission.CMS_DELIVERY_ORDER_BY_JOB + PermissionAction.UPDATE])
  async patchReceiveJobs(
    @Req() context: Request,
    @Body() body: GetReceiveJobsDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.updateReceiveJobs(body.jobs, user);
  }

  @Get('/count')
  @Permissions(permissionCMSGetCountAndMenuCount)
  async getJobsCount(@Req() context: Request) {
    const countArray = await this.jobsService.jobCount(context);
    if (countArray) {
      const value = {
        'all-pending': countArray[0],
        'all-estimating': countArray[1],
        'my-estimating': countArray[2],
      };
      return value;
    }
  }

  @Get('/shipping-status')
  // @Roles([Role.RECEIVE])
  @Permissions([Permission.CMS_DELIVERY_ORDER_BY_JOB + PermissionAction.VIEW])
  async getShippingStatus(@Req() _: Request) {
    return this.jobsService.shippingStatus();
  }

  @Get('/:id')
  @Permissions(permissionCMSGetJobId)
  async getJob(@Req() context: Request, @Param('id') id: string) {
    let job = await super.findOne(context, id);
    job = await this.shopJobsService.getMediaUrl(job);
    return job;
  }

  @Get('/menu/count')
  @Permissions(permissionCMSGetCountAndMenuCount)
  async getMenuCount(@Req() _: Request, @WithUser() user: WithUserContext) {
    return this.jobsService.getMenuCount(user);
  }

  @Get('/:type/count')
  @Permissions([
    Permission.CMS_REPAIR_PENDING + PermissionAction.VIEW,
    Permission.CMS_REPAIR_MY + PermissionAction.VIEW,
    Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW,
    Permission.CMS_INSPECTION_MY + PermissionAction.VIEW,
  ])
  async getCountByType(
    @Req() context: Request,
    @Param('type') type: string,
    @WithUser() user: WithUserContext,
  ) {
    return this.jobsService.getCountByType(context, user, type);
  }

  @Post('/product/export')
  @Permissions([Permission.CMS_PRICE_CONFIRM_ALL + PermissionAction.DOWNLOAD])
  async exportProducts(
    @Req() req: Request,
    @Body() body: ExportJobsProductDto,
    @WithUser() user: WithUserContext,
    @Query() query: GetJobsDto,
  ) {
    // set old data should not more than 1 year
    const newReq = req;
    newReq.query['isLessThanAYear'] = 'true';
    newReq.query['productExport'] = 'true';
    //set pagination to false to get all data in on e time
    query['pagination'] = 'false';
    const isUsingCustomQuery = true;

    const jobData = await super.findAll(newReq, query, isUsingCustomQuery);

    const excelFileBuffer = await this.jobsService.exportJobs(
      body,
      user,
      jobData.items,
      'PRODUCT',
    );

    return {
      base64String: excelFileBuffer.toString('base64'),
    };
  }

  @Post('/export')
  @Roles([Role.ADMIN])
  async exportJobs(
    @Req() req: Request,
    @Body() body: ExportJobsDto,
    @WithUser() user: WithUserContext,
    @Query() query: GetJobsDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    // set old data should not more than 1 year
    const newReq = req;
    newReq.query['isLessThanAYear'] = 'true';
    const formatMemoryUsage = (data) =>
      `${Math.round((data / 1024 / 1024) * 100) / 100} MB`;

    const memoryData = process.memoryUsage();
    const memoryUsage = {
      rss: `${formatMemoryUsage(
        memoryData.rss,
      )} -> Resident Set Size - total memory allocated for the process execution`,
      heapTotal: `${formatMemoryUsage(
        memoryData.heapTotal,
      )} -> total size of the allocated heap`,
      heapUsed: `${formatMemoryUsage(
        memoryData.heapUsed,
      )} -> actual memory used during the execution`,
      external: `${formatMemoryUsage(
        memoryData.external,
      )} -> V8 external memory`,
    };

    //set pagination to false to get all data in on e time
    query['pagination'] = 'false';
    const isUsingCustomQuery = true;

    const jobData = await super.findAllWithStream(
      newReq,
      query,
      isUsingCustomQuery,
    );

    const excelFileBuffer = await this.jobsService.exportJobs(
      body,
      user,
      [],
      'REPORT',
      jobData.queryStream,
    );

    return {
      base64String: excelFileBuffer.toString('base64'),
    };
  }

  @Post('/export/stream')
  @Permissions([Permission.CMS_REPORT + PermissionAction.DOWNLOAD])
  async exportJobsStream(
    @Req() req: Request,
    @Body() body: any,
    @WithUser() user: WithUserContext,
    @Query() query: GetJobsDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="excel.xlsx"',
    });

    // set old data should not more than 1 year
    const newReq = req;
    newReq.query['isLessThanAYear'] = 'true';
    const formatMemoryUsage = (data) =>
      `${Math.round((data / 1024 / 1024) * 100) / 100} MB`;

    const memoryData = process.memoryUsage();
    const memoryUsage = {
      rss: `${formatMemoryUsage(
        memoryData.rss,
      )} -> Resident Set Size - total memory allocated for the process execution`,
      heapTotal: `${formatMemoryUsage(
        memoryData.heapTotal,
      )} -> total size of the allocated heap`,
      heapUsed: `${formatMemoryUsage(
        memoryData.heapUsed,
      )} -> actual memory used during the execution`,
      external: `${formatMemoryUsage(
        memoryData.external,
      )} -> V8 external memory`,
    };

    //set pagination to false to get all data in on e time
    query['pagination'] = 'false';
    const isUsingCustomQuery = true;

    const jobData = await super.findAllWithStream(
      newReq,
      query,
      isUsingCustomQuery,
    );

    const result = new StreamableFile(
      (await this.jobsService.exportJobsStream(
        body,
        user,
        [],
        'REPORT',
        jobData.queryStream,
      )) as Readable,
    );

    return result;
  }

  @Post('/sap/export')
  @Permissions([Permission.CMS_SAP_REPORT + PermissionAction.DOWNLOAD])
  async sapExport(
    @Req() req: Request,
    @Body() body: ExportSapDto,
    @WithUser() user: WithUserContext,
    @Query() query: GetJobsDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="excel.xlsx"',
    });

    query['pagination'] = 'false';
    const newReq = req;
    newReq.query['sapExport'] = 'true';
    newReq.query['startDate'] = body.filter.startDate;
    newReq.query['endDate'] = body.filter.endDate;
    newReq.query['dateType'] = body.filter.dateType;
    const isUsingCustomQuery = true;
    const jobData = await super.findAllWithStream(
      newReq,
      query,
      isUsingCustomQuery,
    );

    const result = new StreamableFile(
      (await this.jobsService.exportJobsStream(
        body,
        user,
        [],
        'SAP',
        jobData.queryStream,
      )) as Readable,
    );

    return result;
  }

  @Patch('/:id/product/')
  @Permissions([Permission.CMS_PRICE_CONFIRM_ALL + PermissionAction.UPDATE])
  async product(
    @Param('id') id: string,
    @Body() body: UpdateJobProductDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.updateJobProduct(id, body, user);
  }

  @Post('/:id/assign')
  @Permissions([Permission.CMS_JOB_ALL + PermissionAction.UPDATE])
  async assignJob(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = await this.jobsService.prepareAssignJob(id, user);

    return await super.update(req, id, jobEntity);
  }

  @Post('/:id/suggest-price')
  @Permissions([Permission.CMS_JOB_MY + PermissionAction.UPDATE])
  async suggestPrice(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: SuggestPriceDto,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = await this.jobsService.prepareSuggestPrice(
      id,
      body,
      user,
    );
    const job = await super.update(req, id, jobEntity);
    return job;
  }

  @Patch('/:id/qc/status')
  @Permissions([Permission.CMS_QC_ALL + PermissionAction.UPDATE])
  async qcStatus(
    @Param('id') id: string,
    @Body() body: QCStatusDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.updateQCStatus(id, body, user);
  }

  @Post('/:id/comment')
  @Permissions([Permission.CMS_JOB_MY + PermissionAction.UPDATE])
  async commentJob(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: CommentJobDto,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = await this.jobsService.defaultPrepareJob({
      updatedBy: user.userKey,
      isAdditionalCheckList: true,
    });

    const job = await super.update(req, id, jobEntity);
    // Insert job activities
    await this.jobsService.insertJobActivities(
      job,
      JobActivitiesType.COMMENT,
      {
        summary: Summary.ADMIN_COMMENTED,
        branchId: job.branchId,
        status: job.status,
        shopUserKey: job.shopUserKey,
        adminUserKey: job.adminUserKey,
        displayName: user.name ?? user.userKey,
        message: body.message,
        dataChangeSlug: DataChangeSlug.ADMIN_COMMENTED,
      },
      user,
    );

    return job;
  }

  @Patch('/:id/repair/assign')
  @Permissions([Permission.CMS_REPAIR_PENDING + PermissionAction.UPDATE])
  async assignRepairJob(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
    @Body() body: AssignRepairDto,
  ) {
    return await this.jobsService.assignRepairJob(id, user, body);
  }

  @Patch('/:id/repair/confirm')
  @Permissions([Permission.CMS_REPAIR_MY + PermissionAction.UPDATE])
  async confirmRepairJob(
    @Param('id') id: string,
    @Body() body: RepairConfirmDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.confirmRepairJob({ id, user, body });
  }

  @Patch('/:id/inspect')
  @Permissions([Permission.CMS_INSPECTION_MY + PermissionAction.UPDATE])
  async updateInspectedJob(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
    @Body() body: InspectJobDto,
  ) {
    return await this.jobsService.updateInspectedJob(id, user, body);
  }

  @Patch('/:id/inspection/assign')
  @Permissions([Permission.CMS_INSPECTION_ALL + PermissionAction.UPDATE])
  async assignInspectJob(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.assignInspectJob(id, user);
  }

  @Get('/inspection/start-date')
  @Permissions([Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW])
  async getMinInspectionDate(@WithUser() user: WithUserContext) {
    return await this.jobsService.getMinInspectDate();
  }

  @Get('/inspection/month-view/:year/:month')
  @Permissions([Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW])
  async getMonthlyInspection(
    @Param('year') year: number,
    @Param('month') month: number,
    @WithUser() user: WithUserContext,
  ) {
    const data = await this.jobsService.getMonthlyInspect({ year, month });
    return data.monthlyInspectData;
  }

  @Get('/:id/inspection/transaction')
  @Permissions([Permission.CMS_INSPECTION_ALL + PermissionAction.DOWNLOAD])
  async getTransactionBase64(@Param('id') id: string) {
    const base64 = await this.jobsService.getTransactionInspection(id);
    return { base64 };
  }

  @Patch('/:id/incomplete-shipping')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async updateIncompleteShipping(
    @Param('id') id: string,
    @Body() body: RejectAODto,
    @WithUser() user: WithUserContext,
  ) {
    const result = await this.jobsService.updateIncompleteShipping(
      id,
      body,
      user,
    );
    return result;
  }

  @Patch('/:id/incomplete-receiving')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async updateIncompleteReceiving(
    @Param('id') id: string,
    @Body() body: RejectAODto,
    @WithUser() user: WithUserContext,
  ) {
    const result = await this.jobsService.updateIncompleteReceiving(
      id,
      body,
      user,
    );
    return result;
  }

  @Patch('/:id/incomplete-lost')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async updateIncompleteLost(
    @Param('id') id: string,
    @Body() body: LostAODto,
    @WithUser() user: WithUserContext,
  ) {
    const result = await this.jobsService.updateIncompleteLost(id, body, user);
    return result;
  }

  @Patch('/operation-cost-config')
  @Roles([Role.ADMIN])
  async setOperationConfig(
    @Body() body: SetOperationCostDto,
    @WithUser() user: WithUserContext,
  ) {
    return this.jobsService.setOperationCost(body, user.company);
  }

  @Patch('/standard-config')
  @Roles([Role.ADMIN])
  async setStandardConfig(
    @Body() body: SetStandardConfigDto,
    @WithUser() user: WithUserContext,
  ) {
    return this.jobsService.setStandardConfig(
      body.configType,
      body.configValue,
      user,
    );
  }

  @Patch('/confirm-price')
  @Permissions([Permission.CMS_PRICE_CONFIRM_ALL + PermissionAction.UPDATE])
  async confirmPrice(
    @Body() body: ConfirmPriceDto,
    @WithUser() user: WithUserContext,
  ) {
    return this.jobsService.confirmPrice(body, user);
  }

  @Get('/:id/presign-upload/:key')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async getPresigned(
    @Param('id') id: string,
    @Param('key') key: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.getPresigned(id, key, user);
  }

  @Post('/get-presign')
  async getMediaUrl(@Body() body: GetMediaUrlDto) {
    return await this.shopJobsService.getMediaUrlFromPath(body);
  }
}
