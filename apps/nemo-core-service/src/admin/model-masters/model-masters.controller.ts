import {
  Controller,
  Get,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
  Put,
  Param,
  Body,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { ModelMasterEntity } from '../../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';
import {
  ModelMastersService,
  transformQueryOrderByForModelMasterCrudController,
} from './model-masters.service';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
} from '../../config';
import { GetModelMastersDto } from './dto/get-model-masters-dto';
import { WithUser, Permissions } from '../../decorators';
import { FileInterceptor } from '@nestjs/platform-express';
import { WithUserContext } from '../../interfaces';
import { UpdateModelMasterDto } from './dto/update-model-masters-dto';
import { ApiBody } from '@nestjs/swagger';

@Controller('v1/admin/model-masters')
export class ModelMastersController extends CrudController<ModelMasterEntity> {
  constructor(
    @InjectRepository(ModelMasterEntity) repo: Repository<ModelMasterEntity>,
    private readonly modelMastersService: ModelMastersService,
  ) {
    super(ModelMasterEntity, 'model_master', repo, {
      resourceKeyPath: 'modelKey',
      order: { updatedAt: 'asc' },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<ModelMasterEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery
          .andWhere(`r.companyId = :company`, {
            company: company,
          })
          .andWhere(`r.modelKey NOT IN ('default')`);
      },
      searchFilter: async (
        context: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<ModelMasterEntity>,
      ) => this.modelMastersService.buildSearchQuery(context, listQuery),
      afterLoad: [
        async (context, data, isMany): Promise<any[]> =>
          isMany ? this.modelMastersService.afterLoad(data) : data,
      ],
    });
  }

  @Get()
  @Permissions([
    Permission.CMS_MODEL_MANAGE + PermissionAction.VIEW,
    Permission.CMS_MODEL_COST_MANAGE + PermissionAction.VIEW,
  ])
  async getModelMasters(
    @Req() context: Request,
    @Query() query: GetModelMastersDto,
  ) {
    query.orderBy = transformQueryOrderByForModelMasterCrudController(
      query?.orderBy,
    );

    return super.findAll(context, query);
  }

  @Get('/export')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.DOWNLOAD])
  async getFile(@Req() context: Request, @Query() query: GetModelMastersDto) {
    let orderBy = 'modelKey asc';
    query.orderBy = orderBy;
    const test = await super.findAll(context, {
      ...query,
      pagination: 'false',
    });
    const buf = await this.modelMastersService.exportExcelModelMasterPrice(
      test.items,
    );

    return {
      base64String: buf.toString('base64'),
    };
  }

  @Get('/:id')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.VIEW])
  async getModelMasterByModelKey(
    @Req() context: Request,
    @Param('id') id: string,
  ) {
    return await super.findOne(context, id);
  }

  @Put()
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Req() req: Request,
    @WithUser() user: WithUserContext,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const xCompany = req.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.modelMastersService.putModelMaster(
      company ? company : undefined,
      file,
      user.userKey,
    );
  }

  @Put('/average-cost')
  @Permissions([Permission.CMS_MODEL_COST_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadVoucher(
    @Req() req: Request,
    @WithUser() user: WithUserContext,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.modelMastersService.uploadAverageCost({
      file,
      user: user.userKey,
      company: user.company,
    });
  }

  @Put('/:id')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.UPDATE])
  @ApiBody({ type: UpdateModelMasterDto })
  async updateProductByModelKey(
    @Param('id') id: string,
    @Body() body: UpdateModelMasterDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.modelMastersService.updateModelMaster(
      id,
      body,
      user.userKey,
    );
  }

  @Get('/export/average-cost')
  @Permissions([Permission.CMS_MODEL_COST_MANAGE + PermissionAction.DOWNLOAD])
  async getAvgCostFile(
    @Req() context: Request,
    @Query() query: GetModelMastersDto,
  ) {
    let orderBy = 'modelKey asc';
    query.orderBy = orderBy;
    const test = await super.findAll(context, {
      ...query,
      pagination: 'false',
    });
    const buf = await this.modelMastersService.exportAvgCost(test.items);

    return {
      base64String: buf.toString('base64'),
    };
  }
}
