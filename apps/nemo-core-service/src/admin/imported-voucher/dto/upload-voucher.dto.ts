import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const UploadVoucherSchema = z.object({
  providerName: z
    .string({
      required_error: 'Provider name is required',
      invalid_type_error: 'Provider name must be a string',
    })
    .min(1, { message: 'Provider name is required' })
    .max(30, { message: 'Provider name must not exceed 30 characters' })
    .regex(/^[a-zA-Z0-9]+$/, {
      message: 'Provider name can only contain letters (a-z, A-Z) and numbers (0-9)',
    })
    .transform((val) => val.trim()),
});

export const UploadVoucherOpenApi = zodToOpenAPI(
  UploadVoucherSchema,
) as SchemaObject;

export class UploadVoucherDto extends createZodDto(UploadVoucherSchema) {}
