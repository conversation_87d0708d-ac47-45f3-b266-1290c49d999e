import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { SelectQueryBuilder, Repository } from 'typeorm';
import { Request } from 'express';
// Import ImportedVoucher module
import { GeneralActivitiesEntity, ImportedVoucherEntity } from '../../entities';
// Import other service
import {
  ExcelManagerService,
  Options,
  IConvertToType,
} from '../../excel/excel-manager.service';
import { GetCountVocuherDto } from './dto/get-count-voucher.dto';

@Injectable()
export class ImportedVoucherService {
  constructor(
    @InjectRepository(ImportedVoucherEntity)
    private readonly importedVoucherRepository: Repository<ImportedVoucherEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepo: Repository<GeneralActivitiesEntity>,
    private readonly excelManagerService: ExcelManagerService,
    @InjectQueue('imported-voucher-queue')
    private readonly queue: Queue,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<ImportedVoucherEntity>,
  ): SelectQueryBuilder<ImportedVoucherEntity> {
    const contractId = context.query.contractId as string;
    const jobId = context.query.jobId as string;

    // Construct filter conditions & apply conditions
    const conditions = [
      contractId && `r.contractId ILIKE '%${contractId}%'`,
      jobId && `contract.jobId ILIKE '%${jobId}%'`,
    ].filter(Boolean);

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  async uploadVoucher({
    file,
    user,
    company,
  }: {
    file: Express.Multer.File;
    user: string;
    company: string;
  }) {
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
      'default-sheet',
      5242880,
    );

    const count = fileData.length;

    const activity = new GeneralActivitiesEntity();
    activity.companyId = company;
    activity.type = 'import_voucher';
    activity.createdBy = user;
    activity.detail = {};
    const { generalActivityId: id } =
      await this.generalActivitiesRepo.save(activity);

    this.queue.add('imported-voucher-upload', {
      vouchers: fileData,
      user,
      company,
      id,
    });

    return { count, id };
  }

  async getVoucherCount(company: string, query: GetCountVocuherDto) {
    const page = query.page?.trim() ? query.page : '1';
    const pageSize = query.pageSize?.trim() ? query.pageSize : '10';
    const limit = Number(pageSize);
    const offset = (Number(page) - 1) * Number(limit);
    const orderBy =
      query.orderBy && query.orderBy.trim() !== ''
        ? query.orderBy
        : 'available asc';
    const sqlQueryCountVoucher = `
    select
      a.voucher_value as voucherValue,
      a.count as total,
      coalesce(b.count,0) as available,
      coalesce(c.count,
      0) as used
    from
      (
      select
        voucher_value,
        count(voucher_id) as count
      from
        core.imported_voucher
      where
        company_id = '${company}'
      group by
        voucher_value
    ) a
    left join (
      select
        voucher_value,
        count(voucher_id) as count
      from
        core.imported_voucher
      where
        contract_id is null and company_id = '${company}'
      group by
        voucher_value
    ) b on
      a.voucher_value = b.voucher_value
    left join (
      select
        voucher_value,
        count(voucher_id) as count
      from
        core.imported_voucher
      where
        contract_id is not null and company_id = '${company}'
      group by
        voucher_value
    ) c on
      a.voucher_value = c.voucher_value
    order by ${orderBy}
    offset ${offset}
    limit ${limit};`;
    const resultCount = await this.importedVoucherRepository
      .createQueryBuilder('importedVoucher')
      .select(`COUNT(DISTINCT importedVoucher.voucherValue)`, 'count')
      .where('importedVoucher.companyId = :companyId', { companyId: company })
      .orderBy('')
      .offset(0)
      .limit(0)
      .getRawOne();
    const result =
      await this.importedVoucherRepository.query(sqlQueryCountVoucher);
    const data = result.map((item) => {
      return {
        voucherValue: item.vouchervalue,
        total: item.total,
        available: item.available,
        used: item.used,
      };
    });
    return {
      items: data,
      paginationResult: {
        page: Number(page),
        pageSize: Number(pageSize),
        totalRecords: Number(resultCount.count ?? 0),
      },
    };
  }

  async exportVoucherTemplate() {
    const sheetName = 'Vouchers';
    this.excelManagerService.options = excelManagerOption;
    return await this.excelManagerService.generateExcelFile([], sheetName);
  }
}

export const excelManagerOption: Options = {
  headers: {
    REF_NO: {
      keyName: 'refNo',
      subHeader: 'No',
      type: IConvertToType.string,
      isRequired: true,
    },
    OTHER_PAYMENT_CODE: {
      keyName: 'otherPaymentCode',
      subHeader: 'Other Payment Code',
      type: IConvertToType.string,
      isRequired: true,
    },
    OTHER_DESCRIPTION: {
      keyName: 'otherDescription',
      subHeader: 'Other Description',
      type: IConvertToType.string,
      isRequired: false,
    },
    VOUCHER_VALUE: {
      keyName: 'voucherValue',
      subHeader: 'Amount',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: 0 },
      },
    },
    REDEMPTION_CODE: {
      keyName: 'redemptionCode',
      subHeader: 'Series Discount',
      type: IConvertToType.string,
      isRequired: true,
    },
    PROVIDER_NAME: {
      keyName: 'providerName',
      subHeader: 'Provider Name',
      type: IConvertToType.string,
      isRequired: true,
    },
  },
  maxRows: 100000,
  headerRowsCount: 2,
};
