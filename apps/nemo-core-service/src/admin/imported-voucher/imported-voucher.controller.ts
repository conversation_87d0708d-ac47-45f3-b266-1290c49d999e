import {
  Controller,
  Req,
  Put,
  Get,
  Query,
  UseInterceptors,
  UploadedFile,
  Body,
} from '@nestjs/common';
import { Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, EntityManager } from 'typeorm';
// Import ImportedVoucher module
import { ImportedVoucherEntity } from '../../entities';
import { ImportedVoucherService } from './imported-voucher.service';
// Import other service
import {
  CrudRemoveRoute,
  WithUser,
  Roles,
  Permissions,
} from '../../decorators';
import { WithUserContext } from '../../interfaces';
import {
  mappingUrlWithCompanyId,
  Permission,
  Role,
  PermissionAction,
} from '../../config';
import { CrudController } from '../../crud';
import { GetCountVocuherDto } from './dto/get-count-voucher.dto';

@CrudRemoveRoute(['removeOne', 'softRemoveOne', 'restore'])
@Controller('v1/admin/imported-voucher')
export class ImportedVoucherController extends CrudController<ImportedVoucherEntity> {
  constructor(
    @InjectRepository(ImportedVoucherEntity)
    repo: Repository<ImportedVoucherEntity>,
    private readonly importedVouchervService: ImportedVoucherService,
  ) {
    super(ImportedVoucherEntity, 'imported_voucher', repo, {
      resourceKeyPath: 'voucherId',
      order: { createdAt: 'asc' },
      defaultPopulate() {
        return ['contract'];
      },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<ImportedVoucherEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      searchFilter: async (
        context: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<ImportedVoucherEntity>,
      ) => this.importedVouchervService.buildSearchQuery(context, listQuery),
    });
  }

  @Get()
  @Roles([Role.SUPER_ADMIN])
  async getVouchers() {
    return; //remove for prd
  }

  @Get('/count')
  @Permissions([Permission.CMS_VOUCHER_MANAGE + PermissionAction.VIEW])
  async getVoucherCount(
    @WithUser() user: WithUserContext,
    @Query() query: GetCountVocuherDto,
  ) {
    return await this.importedVouchervService.getVoucherCount(
      user.company,
      query,
    );
  }

  @Get('/template')
  @Permissions([Permission.CMS_VOUCHER_MANAGE + PermissionAction.DOWNLOAD])
  async getVoucherTemplate() {
    const excelFileBuffer =
      await this.importedVouchervService.exportVoucherTemplate();
    return {
      base64String: excelFileBuffer.toString('base64'),
    };
  }

  @Get('/:id')
  @Roles([Role.SUPER_ADMIN])
  async getVoucher() {
    return; //remove for prd
  }

  @Put('/upload')
  @Permissions([Permission.CMS_VOUCHER_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadVoucher(
    @Req() req: Request,
    @WithUser() user: WithUserContext,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { providerName: string },
  ) {
    return this.importedVouchervService.uploadVoucher({
      file,
      user: user.userKey,
      company: user.company,
      providerName: body.providerName,
    });
  }
}
