import {
  EmailActivitiesType,
  ImportedVoucherEntity,
  SystemConfigEntity,
  UserEntity,
} from '../entities';
import { FirebaseService } from '../firebase/firebase.service';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  IsNull,
  Repository,
  UpdateEvent,
} from 'typeorm';
import { makeCollectionPath } from './helper';
import { ActivitiesType, InboxType } from './job-activities.subscriber';
import { Permission, PermissionAction } from '../config';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { TemplateEmail } from '../../src/smtp/smtp.service';
import { emailGeneralTemplate } from '../../src/utils/email-template';

const notiAtCount = [0, 10, 20, 30, 40, 50];

@EventSubscriber()
export class ImportedVoucherEntitySubscriber
  implements EntitySubscriberInterface<ImportedVoucherEntity>
{
  constructor(
    dataSource: DataSource,
    private readonly firebaseService: FirebaseService,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepo: Repository<SystemConfigEntity>,
    @InjectRepository(UserEntity)
    private readonly UserRepo: Repository<UserEntity>,
    @InjectQueue('email-queue')
    private readonly queue: Queue,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return ImportedVoucherEntity;
  }

  async afterUpdate(event: UpdateEvent<ImportedVoucherEntity>): Promise<void> {
    // Get entity
    const { entity, manager } = event;

    // Prevent invalid entity
    if (!entity) return;

    // Type check voucher entity
    if (entity instanceof ImportedVoucherEntity) {
      const { voucherValue, updatedAt = 0, companyId, providerName } = entity;

      const remainVoucher = await manager.count(ImportedVoucherEntity, {
        where: {
          companyId,
          voucherValue,
          contractId: IsNull(),
          discountContractId: IsNull(),
          providerName,
        },
      });

      if (notiAtCount.includes(remainVoucher)) {
        // add email queue
        const emailTemplate = await this.getEmailTemplate({
          companyId: entity.companyId,
          voucherValue,
          remainVoucher,
        });
        if (emailTemplate) {
          this.queue.add('send-email', emailTemplate);
        }
        // add inbox notification
        await this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX, true),
          this.makeDataInbox({
            updatedAt: +updatedAt,
            remainVoucher,
            voucherValue,
          }),
        );
      }
    }
  }

  private makeDataInbox({
    updatedAt,
    remainVoucher,
    voucherValue,
  }: {
    updatedAt: number;
    remainVoucher: number;
    voucherValue: number;
  }) {
    // Make data voucher firestore
    return {
      type: InboxType.IMPORTED_VOUCHER_OUT_OF_STOCK,
      audience: Permission.CMS_VOUCHER_MANAGE + PermissionAction.UPLOAD,
      updatedAt,
      remainVoucher,
      voucherValue,
    };
  }

  public async getEmailTemplate({
    companyId,
    voucherValue,
    remainVoucher,
  }: {
    companyId: string;
    voucherValue: number;
    remainVoucher: number;
  }) {
    // receiver email
    const targetUsers = await this.UserRepo.find({
      where: {
        companyId,
        userRoleBranch: {
          role: {
            rolePermissions: {
              permissionId: Permission.CMS_VOUCHER_MANAGE,
              upload: true,
            },
          },
        },
      },
    });

    const receiver: string[] = targetUsers
      .map((user) => user.email?.trim() ?? '')
      .filter((email) => email !== '');

    // base url
    const configBaseUrlData = await this.systemConfigRepo.findOne({
      where: { configKey: 'base_url', companyId },
    });
    const baseUrl = configBaseUrlData?.data.cms ?? '';

    if (receiver.length > 0 && baseUrl) {
      // email template
      const sender = process.env.EMAIL_SENDER ?? '<EMAIL>';
      const linkUrl = `${baseUrl}/upload-voucher`;
      const templateEmail: TemplateEmail = {
        mailOption: {
          from: sender,
          to: receiver,
          subject: `จำนวน Voucher มูลค่า ${voucherValue} บาทใกล้หมด`,
          html: emailGeneralTemplate({
            bodyLine: [
              `Voucher มูลค่า ${voucherValue} บาท คงเหลือ ${remainVoucher} ใบ กรุณาอัปโหลดข้อมูล`,
            ],
            linkUrl,
            linkText: 'กดเพื่อไปยังหน้าอัปโหลด',
          }),
        },
        emailActivity: {
          refId: voucherValue.toString(),
          companyId: companyId,
          type: EmailActivitiesType.IMPORTED_VOUCHER_OUT_OF_STOCK,
          detail: { sendEmailInfo: { from: sender, to: receiver } },
        },
      };
      return templateEmail;
    } else {
      return false;
    }
  }
}
