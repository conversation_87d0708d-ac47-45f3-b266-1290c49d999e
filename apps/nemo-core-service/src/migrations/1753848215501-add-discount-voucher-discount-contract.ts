import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDiscountVoucherDiscountContract1753848215501 implements MigrationInterface {
    name = 'AddDiscountVoucherDiscountContract1753848215501'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" ADD "discount_contract_id" character varying(200)`);
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" ADD CONSTRAINT "FK_344fb00234fdd014bc90d3c1160" FOREIGN KEY ("discount_contract_id") REFERENCES "core"."contract"("contract_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" DROP CONSTRAINT "FK_344fb00234fdd014bc90d3c1160"`);
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" DROP COLUMN "discount_contract_id"`);
    }

}
