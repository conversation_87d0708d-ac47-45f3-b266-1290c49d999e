import { MigrationInterface, QueryRunner } from "typeorm";
import { UserVendorTypeMappingEntity } from "../entities";
import { seedDataUserVendorTypeMappingOriginal } from "./1752810454298-create-user-vendor-type-mapping-entity";

const seedDataUserVendorTypeMappingSpecialSp2 = [
            {
                userType: 'WW',
                vendorType: 'MASS',
                providerName: 'WW',
                carrierProgramCode: 'TRUE01DP01'
            },
            {
                userType: 'KINGFISHER',
                vendorType: 'KINGFISHER',
                providerName: 'KINGFISHER',
            },
            {
                userType: 'DTAC',
                vendorType: 'KINGFISHER',
                discountProviderName: 'DTAC',
                partnerName: 'DTAC',
                carrierProgramCode: 'DTAC01DP01'
            },
            {
                userType: 'TUC',
                vendorType: 'KINGFISHER',
                partnerName: 'TUC',
                carrierProgramCode: 'TRUE01DP01'
            },
            {
                userType: 'DB7',
                vendorType: 'KINGFISHER',
                partnerName: 'DB7',
                carrierProgramCode: 'TRUE01DP01'
            }
        ];

export class addNewUVTMColumns1753846744515 implements MigrationInterface {
    name = 'addNewUVTMColumns1753846744515'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add new columns
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" ADD "provider_name" character varying(30)`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" ADD "discount_provider_name" character varying(30)`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" ADD "partner_name" character varying(30)`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" ADD "carrier_program_code" character varying(30)`);

        // Drop all existing seed data
        await queryRunner.query(`DELETE FROM "core"."user_vendor_type_mapping"`);

        // Insert new seed data
        const userVendorMappings = seedDataUserVendorTypeMappingSpecialSp2.map(
            (record) => queryRunner.manager.create(UserVendorTypeMappingEntity, record),
        );

        await queryRunner.manager.save(
            userVendorMappings,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Delete all current data
        await queryRunner.query(`DELETE FROM "core"."user_vendor_type_mapping"`);

        // original seed on 1752810454298
        const userVendorMappings = seedDataUserVendorTypeMappingOriginal.map(
            (record) => queryRunner.manager.create(UserVendorTypeMappingEntity, record),
        );

        await queryRunner.manager.save(
            userVendorMappings,
        );

        // Drop the columns
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" DROP COLUMN "carrier_program_code"`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" DROP COLUMN "partner_name"`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" DROP COLUMN "discount_provider_name"`);
        await queryRunner.query(`ALTER TABLE "core"."user_vendor_type_mapping" DROP COLUMN "provider_name"`);
    }

}
