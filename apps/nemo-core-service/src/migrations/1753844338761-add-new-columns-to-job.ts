import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewColumnsToJob1753844338761 implements MigrationInterface {
    name = 'AddNewColumnsToJob1753844338761'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "shop_grade" character varying(10)`);
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "is_kingfisher_eligible" boolean`);
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "partner_name" character varying(30)`);
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "sale_code" character varying(30)`);
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "offer_reference" character varying(30)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "offer_reference"`);
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "sale_code"`);
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "partner_name"`);
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "is_kingfisher_eligible"`);
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "shop_grade"`);
    }

}
