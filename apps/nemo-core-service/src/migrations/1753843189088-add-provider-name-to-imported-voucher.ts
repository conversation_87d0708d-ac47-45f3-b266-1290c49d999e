import { MigrationInterface, QueryRunner } from "typeorm";

export class AddProviderNameToImportedVoucher1753843189088 implements MigrationInterface {
    name = 'AddProviderNameToImportedVoucher1753843189088'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" ADD "provider_name" character varying(30) NOT NULL DEFAULT 'WW'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."imported_voucher" DROP COLUMN "provider_name"`);
    }
}
