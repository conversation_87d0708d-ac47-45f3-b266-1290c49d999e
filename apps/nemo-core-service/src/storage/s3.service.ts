import {
  GetObjectCommand,
  GetObjectCommandOutput,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';

@Injectable()
export class S3Service {
  // Initial S3 client
  private s3Client: S3Client;

  constructor() {
    // Assign S3 client value
    if (process.env.NODE_ENV === 'production') {
      this.s3Client = new S3Client({
        region: process.env.S3_REGION,
      });
    } else {
      const awsAccessKeyId = process.env.S3_ACCESS_KEY_ID;
      const awsSecretAcessKey = process.env.S3_SECRET_ACCESS_KEY;

      if (!awsAccessKeyId || !awsSecretAcessKey) {
        throw new Error('AWS credentials not found');
      }

      this.s3Client = new S3Client({
        credentials: {
          accessKeyId: awsAccessKeyId,
          secretAccessKey: awsSecretAcessKey,
        },
        region: process.env.S3_REGION,
        endpoint: process.env.S3_ENDPOIN,
        forcePathStyle: true,
      });
    }
  }

  // Direct get to s3
  async getFile(s3Key: string): Promise<GetObjectCommandOutput> {
    // Create a command
    const command = new GetObjectCommand({
      Bucket: process.env.S3_MEDIA_BUCKET_NAME,
      Key: s3Key,
    });

    // Get file from s3
    return await this.s3Client.send(command);
  }

  // Get file with signed url
  async getFileWithSignedUrl(s3Key: string): Promise<string> {
    // Create a command
    const command = new GetObjectCommand({
      Bucket: process.env.S3_MEDIA_BUCKET_NAME,
      Key: s3Key,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn: 3600 * 24 }); // 24 hours
  }

  // Get file with signed url
  async getBufferFile(s3Key: string): Promise<Buffer | undefined> {
    const { Body } = await this.s3Client.send(
      new GetObjectCommand({
        Bucket: process.env.S3_MEDIA_BUCKET_NAME,
        Key: s3Key,
      }),
    );

    if (Body instanceof Readable) {
      // If the body is a stream, convert it to a buffer
      return this.streamToBuffer(Body);
    }
    return undefined;
  }

  streamToBuffer(stream: Readable): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Uint8Array[] = [];

      stream.on('data', (chunk: Uint8Array) => {
        chunks.push(chunk);
      });
      stream.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
      stream.on('error', (error) => {
        reject(error);
      });
    });
  }

  // Direct upload to s3
  async uploadFile(fileStream: Buffer, s3Key: string): Promise<string> {
    // Create a command
    const command = new PutObjectCommand({
      Bucket: process.env.S3_MEDIA_BUCKET_NAME,
      Key: s3Key,
      Body: fileStream,
    });

    // Upload file to s3
    await this.s3Client.send(command);

    return await this.getPreviewUrl(s3Key);
  }

  async getUploadFilePreSignedUrl(s3Key: string): Promise<string> {
    // Create a command
    const command = new PutObjectCommand({
      Bucket: process.env.S3_MEDIA_BUCKET_NAME,
      Key: s3Key,
    });

    return await getSignedUrl(this.s3Client, command, { expiresIn: 3600 });
  }

  async getPreviewUrl(s3Key: string): Promise<string> {
    // S3 Bucket URL
    if (process.env.NODE_ENV === 'production') {
      return `${process.env.BACKEND_BASE_URL}/media/${s3Key}`;
    }
    // Minio public bucket in loca
    return `${process.env.S3_ENDPOIN}/${process.env.S3_MEDIA_BUCKET_NAME}/${s3Key}`;
  }

  async getAssetFile(s3Key: string): Promise<GetObjectCommandOutput> {
    // Create a command
    const command = new GetObjectCommand({
      Bucket: process.env.S3_ASSET_BUCKET_NAME,
      Key: s3Key,
    });

    // Get file from s3
    return await this.s3Client.send(command);
  }
}
