{"name": "nemo-core-service", "version": "2.2.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:fix": "eslint --fix .", "test": "jest --detectOpenHandles --config=./jest.config.js __tests__", "test:single": "jest --detect<PERSON><PERSON><PERSON>andles --config=./jest.config.js", "test:watch": "jest --watch", "test:cov": "jest --coverage --detectOpenHandles --config=./jest.config.js", "test:coverage": "jest --coverage --detectOpenHandles --config=jest.config.js __tests__/services __tests__/admin-services __tests__/common", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ../../node_modules/typeorm/cli", "typeorm:prd": "ts-node ./node_modules/typeorm/cli", "pg:migrate:create": ". ../../scripts/local-env.sh && npm run typeorm -- -d ./src/config/postgres.config.ts migration:generate ./src/migrations/$npm_config_name", "pg:migrate:up": ". ../../scripts/local-env.sh && npm run typeorm migration:run -- -d ./src/config/postgres.config.ts", "pg:migrate:down": ". ../../scripts/local-env.sh && npm run typeorm -- -d ./src/config/postgres.config.ts migration:revert", "pg:migrate:up:prd": "npm run typeorm:prd migration:run -- -d ./dist/src/apps/nemo-core-service/src/config/postgres.config.js", "generate-api": ". ../../scripts/local-env.sh && ts-node ./scripts/generate-api.ts", "convert-master-data": "ts-node ./scripts/convert-master-data/convert.ts", "generate-pdf": "ts-node ./scripts/generate-pdf/generate.ts", "start:poc": "node dist/src/main --max-old-space-size=512"}, "dependencies": {"@aws-sdk/client-s3": "^3.458.0", "@aws-sdk/s3-request-presigner": "^3.458.0", "@nestjs/bull": "^10.1.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.2.8", "@nestjs/core": "^10.4.15", "@nestjs/platform-express": "^10.4.19", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.1", "@pdf-lib/fontkit": "^1.1.1", "aws-sdk-client-mock": "^3.0.1", "axios": "^1.8.2", "bull": "^4.12.2", "bwip-js": "^4.2.0", "cache-manager": "^5.3.2", "cache-manager-ioredis": "^2.1.0", "cache-manager-ioredis-yet": "^1.2.2", "contracts": "*", "nemo-common": "*", "csvtojson": "^2.0.10", "file-type": "^19.0.0", "firebase-admin": "^11.11.0", "google-auth-library": "^9.14.1", "js-yaml": "^4.1.0", "lodash": "*", "luxon": "^3.4.4", "nestjs-zod": "^3.0.0", "pdf-lib": "^1.17.1", "pg": "^8.11.3", "pg-query-stream": "^4.5.5", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "sharp": "^0.33.2", "tozod": "^3.0.0", "ts-mockito": "^2.6.1", "typeorm": "^0.3.17", "typeorm-naming-strategies": "^4.1.0", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@types/cache-manager-ioredis": "^2.0.6", "@types/express": "^4.17.20", "@types/jest": "29.5.7", "@types/js-yaml": "^4.0.8", "@types/lodash": "^4.14.200", "@types/luxon": "^3.3.4", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/supertest": "^2.0.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "29.7.0", "prettier": "^3.0.3", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "29.1.1", "ts-loader": "^9.5.0", "ts-node": "^10.9.1", "tsconfig-paths": "4.2.0", "typescript": "^5.2.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "testRegex": "(/tests/.*|(\\.|/)spec)\\.ts?$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/migrations/**/*.(t|j)s", "!src/filters/**/*.(t|j)s", "!src/firebase/**/*.(t|j)s", "!src/guards/**/*.(t|j)s", "!src/interceptors/**/*.(t|j)s", "!src/interfaces/**/*.(t|j)s", "!src/categories/**/*.(t|j)s", "!src/crud/**/*.(t|j)s", "!src/decorators/**/*.(t|j)s", "!src/middlewares/**/*.(t|j)s", "!src/**/*.controller.(t|j)s", "!src/**/*.module.(t|j)s", "!src/**/dto/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}