{"extends": "../../packages/tsconfig/base.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "paths": {"nemo-common": ["../../packages/common"]}}}