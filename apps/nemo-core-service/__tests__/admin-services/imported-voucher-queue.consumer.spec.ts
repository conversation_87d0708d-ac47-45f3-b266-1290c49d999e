import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  ImportedVoucherEntity,
  GeneralActivitiesEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { ImportedVoucherQueueConsumer } from '../../src/admin/imported-voucher/imported-voucher-queue.consumer';
import { Job } from 'bull';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { generateVoucherExcelDataListMock } from '../../__tests__/mock-data/voucher';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';

describe('Imported Voucher Queue Consumer', () => {
  let importedVoucherQueueConsumer: ImportedVoucherQueueConsumer;
  let importedVoucherRepository: Repository<ImportedVoucherEntity>;
  let generalActivitiesRepository: Repository<GeneralActivitiesEntity>;

  const createQueryBuilder = jest.fn(() => ({
    insert: createQueryBuilder,
    into: createQueryBuilder,
    value: createQueryBuilder,
    execute: createQueryBuilder,
  }));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        ImportedVoucherQueueConsumer,
        {
          provide: getRepositoryToken(ImportedVoucherEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            save: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            decrypt: jest.fn(() => 'decryptedData'),
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    importedVoucherQueueConsumer = module.get<ImportedVoucherQueueConsumer>(
      ImportedVoucherQueueConsumer,
    );
    importedVoucherRepository = module.get<Repository<ImportedVoucherEntity>>(
      getRepositoryToken(ImportedVoucherEntity),
    );
    generalActivitiesRepository = module.get<
      Repository<GeneralActivitiesEntity>
    >(getRepositoryToken(GeneralActivitiesEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockVouchers = [
    {
      refNo: 'refNo1',
      redemptionCode: 'redemptionCode1',
      otherPaymentCode: 'otherPaymentCode1',
      voucherValue: 1000,
    },
    {
      refNo: 'refNo2',
      redemptionCode: 'redemptionCode2',
      otherPaymentCode: 'otherPaymentCode2',
      voucherValue: 2000,
    },
    {
      refNo: 'refNo3',
      redemptionCode: 'redemptionCode3',
      otherPaymentCode: 'otherPaymentCode3',
      voucherValue: 3000,
    },
  ];

  describe('Upload queue', () => {
    it('success', async () => {
      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: mockVouchers,
          user: 'user',
          company: 'company',
          providerName: 'WW',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should fail not found activity', async () => {
      jest
        .spyOn(generalActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers: [...mockVouchers, mockVouchers[0]],
            user: 'user',
            company: 'company',
            providerName: 'WW',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('Should fail duplicate', async () => {
      const vouchers = [...mockVouchers, mockVouchers[0]];

      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers,
            user: 'user',
            company: 'company',
            providerName: 'WW',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.DUPLICATE_DATA_RECORD.code,
        );
        expect((error as BaseExceptionService).data.refNo).toBe('refNo1');
      }
    });
  });

  describe('uploadVoucherErrorDuplicateDatabase', () => {
    it('Should throw error duplicate with DB', async () => {
      const testExcelData = generateVoucherExcelDataListMock(4);
      testExcelData[2].redemptionCode = 'decryptedData';

      const result =
        importedVoucherQueueConsumer.uploadVoucherErrorDuplicateDatabase({
          error: {
            detail: 'Key (redemption_code)=(decryptedData) already exists.',
          },
          fileData: testExcelData,
        });

      expect(result?.refNo).toBe('3');
    });
  });
});
