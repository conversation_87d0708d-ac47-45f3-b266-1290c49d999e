import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  ImportedVoucherEntity,
  GeneralActivitiesEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { ImportedVoucherQueueConsumer } from '../../src/admin/imported-voucher/imported-voucher-queue.consumer';
import { Job } from 'bull';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { generateVoucherExcelDataListMock } from '../../__tests__/mock-data/voucher';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';

describe('Imported Voucher Queue Consumer', () => {
  let importedVoucherQueueConsumer: ImportedVoucherQueueConsumer;
  let importedVoucherRepository: Repository<ImportedVoucherEntity>;
  let generalActivitiesRepository: Repository<GeneralActivitiesEntity>;
  let module: any;

  const createQueryBuilder = jest.fn(() => ({
    insert: createQueryBuilder,
    into: createQueryBuilder,
    value: createQueryBuilder,
    execute: createQueryBuilder,
  }));

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        ImportedVoucherQueueConsumer,
        {
          provide: getRepositoryToken(ImportedVoucherEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            save: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            decrypt: jest.fn(() => 'decryptedData'),
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    importedVoucherQueueConsumer = module.get(ImportedVoucherQueueConsumer);
    importedVoucherRepository = module.get(
      getRepositoryToken(ImportedVoucherEntity),
    );
    generalActivitiesRepository = module.get(
      getRepositoryToken(GeneralActivitiesEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockVouchers = [
    {
      refNo: 'refNo1',
      redemptionCode: 'redemptionCode1',
      otherPaymentCode: 'otherPaymentCode1',
      voucherValue: 1000,
      providerName: 'WW',
    },
    {
      refNo: 'refNo2',
      redemptionCode: 'redemptionCode2',
      otherPaymentCode: 'otherPaymentCode2',
      voucherValue: 2000,
      providerName: 'WW',
    },
    {
      refNo: 'refNo3',
      redemptionCode: 'redemptionCode3',
      otherPaymentCode: 'otherPaymentCode3',
      voucherValue: 3000,
      providerName: 'WW',
    },
  ];

  describe('Upload queue', () => {
    it('success', async () => {
      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: mockVouchers,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should handle vouchers with missing providerName (defaults to WW)', async () => {
      const vouchersWithoutProvider = [
        {
          refNo: 'refNo1',
          redemptionCode: 'redemptionCode1',
          otherPaymentCode: 'otherPaymentCode1',
          voucherValue: 1000,
          // providerName is missing
        },
      ];

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: vouchersWithoutProvider,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should handle vouchers with whitespace-only providerName', async () => {
      const vouchersWithWhitespaceProvider = [
        {
          refNo: 'refNo1',
          redemptionCode: 'redemptionCode1',
          otherPaymentCode: 'otherPaymentCode1',
          voucherValue: 1000,
          providerName: '   \t\n   ', // Only whitespace - will be used as-is
        },
      ];

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: vouchersWithWhitespaceProvider,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should fail not found activity', async () => {
      jest
        .spyOn(generalActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers: [...mockVouchers, mockVouchers[0]],
            user: 'user',
            company: 'company',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('Should fail duplicate', async () => {
      const vouchers = [...mockVouchers, mockVouchers[0]];

      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers,
            user: 'user',
            company: 'company',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.DUPLICATE_DATA_RECORD.code,
        );
        expect((error as BaseExceptionService).data.refNo).toBe('refNo1');
      }
    });

    it('Should accept any providerName - special characters allowed', async () => {
      const vouchersWithSpecialCharProvider = [
        {
          ...mockVouchers[0],
          providerName: 'WW@123', // Contains special character - now allowed
        },
      ];

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: vouchersWithSpecialCharProvider,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should accept long providerName', async () => {
      const vouchersWithLongProvider = [
        {
          ...mockVouchers[0],
          providerName: 'A'.repeat(50), // Long provider name - validation moved to excel options
        },
      ];

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: vouchersWithLongProvider,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should accept empty providerName and default to WW', async () => {
      const vouchersWithEmptyProvider = [
        {
          ...mockVouchers[0],
          providerName: '', // Empty string - should default to WW
        },
      ];

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: vouchersWithEmptyProvider,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(
        importedVoucherRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it.skip('Should handle database transaction error', async () => {
      const mockError = {
        detail: 'Key (redemption_code)=(testCode) already exists.',
      };

      jest
        .spyOn(importedVoucherRepository.manager, 'transaction')
        .mockImplementationOnce(async (callback: any) => {
          const mockManager = {
            createQueryBuilder: jest.fn().mockReturnValue({
              insert: jest.fn().mockReturnThis(),
              into: jest.fn().mockReturnThis(),
              values: jest.fn().mockReturnThis(),
              execute: jest.fn().mockRejectedValue(mockError),
            }),
          };
          return callback(mockManager);
        });

      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers: mockVouchers,
            user: 'user',
            company: 'company',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_ALREADY_UPLOADED.code,
        );
      }
    });
  });

  describe('uploadVoucherErrorDuplicateDatabase', () => {
    it('Should throw error duplicate with DB', async () => {
      const testExcelData = generateVoucherExcelDataListMock(4);
      testExcelData[2].redemptionCode = 'decryptedData';

      const result =
        importedVoucherQueueConsumer.uploadVoucherErrorDuplicateDatabase({
          error: {
            detail: 'Key (redemption_code)=(decryptedData) already exists.',
          },
          fileData: testExcelData,
        });

      expect(result?.refNo).toBe('3');
    });

    it('Should handle error with non-redemption_code key', async () => {
      const testExcelData = generateVoucherExcelDataListMock(4);

      const result =
        importedVoucherQueueConsumer.uploadVoucherErrorDuplicateDatabase({
          error: {
            detail: 'Key (other_field)=(someValue) already exists.',
          },
          fileData: testExcelData,
        });

      expect(result).toBeUndefined();
    });

    it('Should handle error with malformed detail string', async () => {
      const testExcelData = generateVoucherExcelDataListMock(4);

      const result =
        importedVoucherQueueConsumer.uploadVoucherErrorDuplicateDatabase({
          error: {
            detail: 'Invalid error format without parentheses',
          },
          fileData: testExcelData,
        });

      expect(result).toBeUndefined();
    });

    it('Should handle error when redemption code not found in file data', async () => {
      const testExcelData = generateVoucherExcelDataListMock(4);
      // Use a base64 encoded string that will decrypt to something not in the test data
      const base64EncodedCode =
        Buffer.from('nonExistentCode').toString('base64');

      // This test expects the method to throw an error when target is not found
      expect(() => {
        importedVoucherQueueConsumer.uploadVoucherErrorDuplicateDatabase({
          error: {
            detail: `Key (redemption_code)=(${base64EncodedCode}) already exists.`,
          },
          fileData: testExcelData,
        });
      }).toThrow();
    });
  });

  describe('Error handling and Firebase integration', () => {
    it('Should update Firebase with error status when exception occurs', async () => {
      const firebaseService = module.get(FirebaseService);
      const addDataSpy = jest.spyOn(firebaseService, 'addData');

      try {
        await importedVoucherQueueConsumer.importedVoucherUpload({
          data: {
            vouchers: [...mockVouchers, mockVouchers[0]], // Duplicate to cause error
            user: 'user',
            company: 'company',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        // Error is expected
      }

      expect(addDataSpy).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          result: 'error',
          error: expect.objectContaining({
            code: expect.any(String),
            refNo: expect.any(String),
          }),
        }),
      );
    });

    it('Should update Firebase with success status when upload succeeds', async () => {
      const firebaseService = module.get(FirebaseService);
      const addDataSpy = jest.spyOn(firebaseService, 'addData');

      await importedVoucherQueueConsumer.importedVoucherUpload({
        data: {
          vouchers: mockVouchers,
          user: 'user',
          company: 'company',
          id: 'id',
        },
      } as Job);

      expect(addDataSpy).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          result: 'success',
          error: null,
        }),
      );
    });
  });
});
