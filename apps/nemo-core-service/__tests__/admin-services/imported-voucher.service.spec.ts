import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { BASE_EXCEPTIONS } from '../../src/config';
import {
  GeneralActivitiesEntity,
  ImportedVoucherEntity,
} from '../../src/entities';
import { BaseExceptionService } from '../../src/exceptions';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { ExcelManagerService } from '../../src/excel/excel-manager.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import {
  ImportedVoucherService,
  excelManagerOption,
} from '../../src/admin/imported-voucher/imported-voucher.service';
import {
  generateVoucherExcelDataListMock,
  count,
  mockVoucherCount,
  mockVoucherCountQuery,
} from '../mock-data/voucher';

describe('ImportedVoucherService', () => {
  let importedVoucherService: ImportedVoucherService;
  let excelManagerService: ExcelManagerService;

  const createQueryBuilder = jest.fn(() => ({
    select: createQueryBuilder,
    where: createQueryBuilder,
    orderBy: createQueryBuilder,
    offset: createQueryBuilder,
    limit: createQueryBuilder,
    insert: createQueryBuilder,
    into: createQueryBuilder,
    value: createQueryBuilder,
    execute: createQueryBuilder,
    getRawOne: jest.fn(() => count),
  }));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(excelManagerOption),
      ],
      providers: [
        ImportedVoucherService,
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockResolvedValue({ generalActivityId: 'test-id' }),
          },
        },
        {
          provide: getRepositoryToken(ImportedVoucherEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            query: jest.fn(() => mockVoucherCountQuery),
            save: jest.fn().mockReturnThis(),
            map: jest.fn(() => mockVoucherCount),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            decrypt: jest.fn(() => 'decryptedData'),
          },
        },
        {
          provide: getQueueToken('imported-voucher-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();
    importedVoucherService = module.get<ImportedVoucherService>(
      ImportedVoucherService,
    );
    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should success upload', async () => {
    const testExcelData = generateVoucherExcelDataListMock(4);
    const excelBuffer = await excelManagerService.generateExcelFile(
      testExcelData,
      'Sheet name',
    );
    const result = await importedVoucherService.uploadVoucher({
      file: {
        buffer: excelBuffer,
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      } as unknown as Express.Multer.File,
      user: 'user',
      company: 'company',
    });

    expect(result).toEqual({ count: 4, id: expect.any(String) });
  });

  it.skip('Should fail due to duplicate in file', async () => {
    const testExcelData = generateVoucherExcelDataListMock(4).map(
      (item: any) => {
        if (['1', '2'].includes(item.refNo)) {
          return { ...item, redemptionCode: 'duplicate' };
        } else return { item };
      },
    );
    const excelBuffer = await excelManagerService.generateExcelFile(
      testExcelData,
      'Sheet name',
    );
    try {
      await importedVoucherService.uploadVoucher({
        file: {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File,
        user: 'user',
        company: 'company',
      });
      fail('Expected method to throw an error');
    } catch (error) {
      expect(error).toBeInstanceOf(BaseExceptionService);
      expect((error as BaseExceptionService).code).toBe(
        BASE_EXCEPTIONS.DUPLICATE_DATA_RECORD.code,
      );
      expect((error as BaseExceptionService).data.refNo).toBe('2');
    }
  });

  describe('getVoucherCount', () => {
    it('Should return voucher count with default param', async () => {
      const companyTest = 'company';
      const queryTest = {
        page: '',
        pageSize: '',
        orderBy: '',
      };
      const result = await importedVoucherService.getVoucherCount(
        companyTest,
        queryTest,
      );
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('paginationResult');
    });

    it('Should return voucher count with param', async () => {
      const companyTest = 'company';
      const queryTest = {
        page: '1',
        pageSize: '2',
        orderBy: 'total desc',
      };
      const result = await importedVoucherService.getVoucherCount(
        companyTest,
        queryTest,
      );
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('paginationResult');
    });
  });

  describe('get Voucher Template', () => {
    it('Should return excel as buffer', async () => {
      const data = await importedVoucherService.exportVoucherTemplate();
      expect(data).toBeInstanceOf(Buffer);
    });
  });
});
