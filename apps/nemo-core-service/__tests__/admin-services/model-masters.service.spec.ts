import { getRepositoryToken } from '@nestjs/typeorm';
import {
  CompanyEntity,
  ModelMasterEntity,
  SystemConfigEntity,
  JobEntity,
  ModelChecklistEntity,
  ModelMasterGradeDetail,
  ChecklistType,
  QuestionType,
  ModelPriceActivitiesEntity,
} from '../../src/entities';
import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  ModelMastersService,
  excelManagerOption,
  transformQueryOrderByForModelMasterCrudController,
} from '../../src/admin/model-masters/model-masters.service';
import {
  mockSuccessColumnModelMaster,
  mockRowDataModalMaster,
} from '../mock-data/excel-manager';
import * as ExcelJS from 'exceljs';
import { Readable } from 'stream';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { UpdateModelMasterDto } from 'src/admin/model-masters/dto/update-model-masters-dto';
import { Repository } from 'typeorm';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';

const manager = {
  transaction: jest.fn().mockReturnThis(),
  getRepository: jest.fn().mockReturnThis(),
  connection: {
    createQueryRunner: jest.fn().mockReturnThis(),
    connect: jest.fn().mockReturnThis(),
    startTransaction: jest.fn().mockReturnThis(),
    release: jest.fn().mockReturnThis(),
    rollbackTransaction: jest.fn().mockReturnThis(),
    commitTransaction: jest.fn().mockReturnThis(),
    manager: {
      getRepository: jest.fn().mockReturnThis(),
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      orIgnore: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      save: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ raw: [] }),
      delete: jest.fn().mockReturnThis(),
      count: jest.fn(),
    },
  },
};

describe('ModelMastersService', () => {
  let modelMastersService: ModelMastersService;
  let modelMastersRepository: Repository<ModelMasterEntity>;
  let modelChecklistRepository: Repository<ModelChecklistEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(excelManagerOption),
      ],
      providers: [
        ModelMastersService,
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            find: jest.fn(() => []),
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            manager,
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn(() => ({
              companyId: 'WW',
            })),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelChecklistEntity),
          useValue: {
            find: jest.fn().mockReturnValue([{ companyId: 'WW' }]),
          },
        },
        {
          provide: getRepositoryToken(ModelPriceActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    modelMastersService = module.get<ModelMastersService>(ModelMastersService);
    modelMastersRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );
    modelChecklistRepository = module.get<Repository<ModelChecklistEntity>>(
      getRepositoryToken(ModelChecklistEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('put model master', () => {
    it('should sucessfully save', async () => {
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnModelMaster;

      for (let i = 0; i < mockRowDataModalMaster.length; i++) {
        worksheet.addRow(mockRowDataModalMaster[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      const result = await modelMastersService.putModelMaster(
        'WW',
        mockFile,
        'test_user',
      );
      expect(result).toBeNull();
    });
  });

  describe('update model master', () => {
    const mockModelChecklists = [
      {
        id: 'checklist1',
        companyId: 'WW',
        functionKey: 'cond1',
        functionSection: 'remobie_check_list',
        checklistType: ChecklistType.MODULE,
      },
      {
        id: 'checklist2',
        companyId: 'WW',
        functionKey: 'cond3',
        functionSection: 'remobie_check_list',
        checklistType: ChecklistType.MODULE,
      },
      {
        id: 'checklist3',
        companyId: 'WW',
        functionKey: 'cond2',
        functionSection: 'product_information',
        checklistType: ChecklistType.QUESTION,
        questionType: QuestionType.SELECTION,
        questionChoices: [{ id: 'th' }],
      },
    ] as ModelChecklistEntity[];

    const validPayload: UpdateModelMasterDto = {
      companyId: 'WW',
      modelKey: 'default',
      modelMaster: {
        systemCodeList: ['TESTSYSCODE', 'TESTSYSCODE2'],
        modelMasterGrades: [
          {
            grade: 'B',
            purchasePrice: '3000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'A',
            purchasePrice: '3500.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: '2500.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: '2000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
      },
      modelMasterFunction: [
        {
          keyCond: 'remobie_check_list.cond1=functional',
          penalties: '230.00',
          action: 'CREATE',
        },
        {
          keyCond: 'remobie_check_list.cond1=non_functional',
          penalties: '230.00',
          action: 'CREATE',
        },
        {
          keyCond: 'remobie_check_list.cond1=skip',
          penalties: '230.00',
          action: 'CREATE',
        },
        {
          keyCond: 'product_information.cond2=th',
          penalties: '-2000.00',
          action: 'UPDATE',
        },
        {
          keyCond: 'remobie_check_list.cond3=functional',
          penalties: '230.00',
          action: 'DELETE',
        },
        {
          keyCond: 'remobie_check_list.cond3=non_functional',
          penalties: '230.00',
          action: 'DELETE',
        },
        {
          keyCond: 'remobie_check_list.cond3=skip',
          penalties: '230.00',
          action: 'DELETE',
        },
      ],
    };

    const invalidPayload: UpdateModelMasterDto = {
      companyId: 'WW',
      modelKey: 'default',
      modelMaster: {
        systemCodeList: ['TESTSYSCODE', 'TESTSYSCODE2'],
        modelMasterGrades: [
          {
            grade: 'B',
            purchasePrice: '3000.10',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'A',
            purchasePrice: '-12.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: '0.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: '2000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
      },
      modelMasterFunction: [
        {
          keyCond: 'remobie_check_list.cond1=functional',
          penalties: '230.00',
          action: 'CREATE_WRONG',
        },
        {
          keyCond: 'product_information.cond2=th',
          penalties: '-2000.10',
          action: 'UPDATE',
        },
        {
          keyCond: 'remobie_check_list.cond3=functional',
          penalties: '230.00',
          action: 'DELETE',
        },
        {
          keyCond: '',
          penalties: '0',
          action: 'DELETE',
        },
      ],
    };

    it('should sucessfully save', async () => {
      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      const result = await modelMastersService.updateModelMaster(
        'default',
        validPayload,
        'test_user',
      );

      expect(result).toBeNull();
    });

    it('should return not found', async () => {
      jest.spyOn(modelMastersRepository, 'findOneBy').mockResolvedValue(null);

      try {
        await modelMastersService.updateModelMaster(
          'default',
          validPayload,
          'test_user',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
        expect((error as BaseExceptionService).data).toStrictEqual([
          'MASTER_MODEL_NOT_FOUND',
        ]);
      }
    });

    it('should return error message', async () => {
      try {
        await modelMastersService.updateModelMaster(
          'default',
          invalidPayload,
          'test_user',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toStrictEqual([
          'INPUT_POSITIVE_NUMBER_INVALID',
          'PURCHASE_PRICE_INVALID',
          'ACTION_INVALID',
          'INPUT_INTEGER_INVALID',
          'FUNCTION_KEY_COND_INVALID',
          'FUNCTION_INPUT_INVALID',
        ]);
      }
    });

    it('should return error message', async () => {
      try {
        Object.defineProperty(modelMastersRepository, 'manager', {
          value: {
            ...manager,
            connection: {
              ...manager.connection,
              manager: {
                ...manager.connection.manager,
                count: jest.fn().mockReturnValue(0),
              },
            },
          },
        });

        await modelMastersService.updateModelMaster(
          'default',
          {
            ...validPayload,
            modelMasterFunction: [
              {
                keyCond: 'remobie_check_list.cond3=functional',
                penalties: '230.00',
                action: 'DELETE',
              },
            ],
          },
          'test_user',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toStrictEqual([
          'QUESTION_AT_LEAST_1',
          'CHECK_LIST_AT_LEAST_1',
          'FUNCTION_INPUT_INVALID',
        ]);
      }
    });
  });

  describe('validateModelMasterGrades', () => {
    const validPayload: ModelMasterGradeDetail[] = [
      {
        grade: 'B',
        purchasePrice: '3000.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'A',
        purchasePrice: '3500.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'C',
        purchasePrice: '2500.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'D',
        purchasePrice: '2000.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
    ];

    const invalidPayload: ModelMasterGradeDetail[] = [
      {
        grade: 'AB',
        purchasePrice: '0.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'A',
        purchasePrice: '3500.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'C',
        purchasePrice: '0.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'D',
        purchasePrice: '2000.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
    ];

    it('should return empty array', async () => {
      const result =
        modelMastersService.validateModelMasterGrades(validPayload);
      expect(result).toEqual([]);
    });

    it('should return string array', async () => {
      const result =
        modelMastersService.validateModelMasterGrades(invalidPayload);

      expect(result).toContain('GRADE_INVALID');
      expect(result).toContain('PURCHASE_PRICE_INVALID');
      expect(result).toContain('INPUT_POSITIVE_NUMBER_INVALID');
    });
  });

  describe('export model master price', () => {
    it('should sucessfully get', async () => {
      const data = [];
      const result =
        await modelMastersService.exportExcelModelMasterPrice(data);

      expect(result).toBeInstanceOf(Buffer);
    });
  });

  describe('transformQueryOrderByForModelMasterCrudController', () => {
    describe('when orderBy is falsy', () => {
      it('should return undefined when orderBy is undefined', () => {
        const result =
          transformQueryOrderByForModelMasterCrudController(undefined);
        expect(result).toBeUndefined();
      });

      it('should return empty string when orderBy is empty string', () => {
        const result = transformQueryOrderByForModelMasterCrudController('');
        expect(result).toBe('');
      });
    });

    describe('when orderBy is modelKey', () => {
      it('should return original orderBy when it contains modelKey', () => {
        const orderBy = 'modelKey asc';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('modelKey asc');
      });
    });

    describe('when orderBy contains model identifier fields', () => {
      it('should transform orderBy when field is model', () => {
        const orderBy = 'model asc';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('modelIdentifiers model asc,modelKey asc');
      });

      it('should transform orderBy when field is brand', () => {
        const orderBy = 'brand desc';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('modelIdentifiers brand desc,modelKey asc');
      });

      it('should transform orderBy with brand field and only field name', () => {
        const orderBy = 'brand';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('modelIdentifiers brand,modelKey asc');
      });
    });

    describe('when orderBy contains non-model identifier fields', () => {
      it('should add modelKey asc for regular fields', () => {
        const orderBy = 'ownerName desc';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('ownerName desc,modelKey asc');
      });

      it('should add modelKey asc when field has no sort direction', () => {
        const orderBy = 'ownerName';
        const result =
          transformQueryOrderByForModelMasterCrudController(orderBy);
        expect(result).toBe('ownerName,modelKey asc');
      });
    });
  });
});
