import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
  Repository,
} from 'typeorm';
import {
  ImportedVoucherEntity,
  SystemConfigEntity,
  UserEntity,
} from '../../src/entities';
import { Test } from '@nestjs/testing';
import { ImportedVoucherEntitySubscriber } from '../../src/subscriber';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { getQueueToken } from '@nestjs/bull';
import { Queue } from 'bull';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TemplateEmail } from '../../src/smtp/smtp.service';

describe('Issue Report Subscriber', () => {
  let ImportedVoucherSubscriber: ImportedVoucherEntitySubscriber;
  let entityManager: EntityManager;
  let firebaseService: FirebaseService;
  let userRepo: Repository<UserEntity>;
  let systemConfigRepo: Repository<SystemConfigEntity>;
  let emailQueue: Queue;

  const importedVoucherEntity = new ImportedVoucherEntity();
  importedVoucherEntity.companyId = 'test-company';
  importedVoucherEntity.voucherValue = 1000;
  importedVoucherEntity.providerName = 'WW';

  const mockedEvent = {
    entity: importedVoucherEntity,
    databaseEntity: {} as ImportedVoucherEntity,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            count: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useClass: Repository,
        },
        {
          provide: getQueueToken('email-queue'),
          useValue: {
            add: jest.fn(),
          },
        },
        ImportedVoucherEntitySubscriber,
      ],
    }).compile();

    ImportedVoucherSubscriber = module.get<ImportedVoucherEntitySubscriber>(
      ImportedVoucherEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
    firebaseService = module.get<FirebaseService>(FirebaseService);
    userRepo = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    systemConfigRepo = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
    emailQueue = module.get<Queue>(getQueueToken('email-queue'));
  });

  describe('after update ImportedVoucherEntitySubscriber', () => {
    afterEach(() => {
      jest.clearAllMocks();
      jest.restoreAllMocks();
    });

    it.each([
      [50, true],
      [40, true],
      [30, true],
      [20, true],
      [10, true],
      [0, true],
      [51, false],
      [49, false],
    ])(
      `afterUpdate call with count %s expect call firebase %s with mock template true`,
      async (remain, isCallFirebase) => {
        jest
          .spyOn(ImportedVoucherSubscriber, 'getEmailTemplate')
          .mockResolvedValueOnce({} as TemplateEmail);

        jest.spyOn(entityManager, 'count').mockResolvedValueOnce(remain);
        await ImportedVoucherSubscriber.afterUpdate({
          ...mockedEvent,
          manager: entityManager as EntityManager,
        });

        expect(entityManager.count).toHaveBeenCalledWith(
          ImportedVoucherEntity,
          {
            where: {
              companyId: 'test-company',
              voucherValue: 1000,
              contractId: expect.anything(),
              providerName: 'WW',
            },
          },
        );

        if (isCallFirebase) {
          expect(firebaseService.addData).toHaveBeenCalled();
          expect(emailQueue.add).toHaveBeenCalled();
        } else {
          expect(firebaseService.addData).toHaveBeenCalledTimes(0);
          expect(emailQueue.add).toHaveBeenCalledTimes(0);
        }
      },
    );

    it('afterUpdate call with mock template false', async () => {
      jest
        .spyOn(ImportedVoucherSubscriber, 'getEmailTemplate')
        .mockResolvedValueOnce(false);

      jest.spyOn(entityManager, 'count').mockResolvedValueOnce(10);
      await ImportedVoucherSubscriber.afterUpdate({
        ...mockedEvent,
        manager: entityManager as EntityManager,
      });

      expect(entityManager.count).toHaveBeenCalledWith(ImportedVoucherEntity, {
        where: {
          companyId: 'test-company',
          voucherValue: 1000,
          contractId: expect.anything(),
          providerName: 'WW',
        },
      });

      expect(firebaseService.addData).toHaveBeenCalled();
      expect(emailQueue.add).toHaveBeenCalledTimes(0);
    });

    it('afterUpdate call with no entity', async () => {
      jest
        .spyOn(ImportedVoucherSubscriber, 'getEmailTemplate')
        .mockResolvedValueOnce({} as TemplateEmail);

      await ImportedVoucherSubscriber.afterUpdate({
        ...mockedEvent,
        entity: undefined as any,
        manager: entityManager as EntityManager,
      });

      expect(firebaseService.addData).toHaveBeenCalledTimes(0);
      expect(emailQueue.add).toHaveBeenCalledTimes(0);
    });

    it('listenTo', async () => {
      const result = ImportedVoucherSubscriber.listenTo();
      expect(result).toBe(ImportedVoucherEntity);
    });
  });

  describe('getEmailTemplate', () => {
    const targetUsers = [
      { email: '<EMAIL>' },
      { email: '<EMAIL>' },
      {},
    ] as UserEntity[];
    const configBaseUrlData = {
      data: { cms: 'http://example.com' },
    } as SystemConfigEntity;
    const prop = {
      companyId: 'companyId',
      voucherValue: 10,
      remainVoucher: 10,
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return email template', async () => {
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);

      const result = await ImportedVoucherSubscriber.getEmailTemplate(prop);

      expect(result).toBeTruthy();
    });

    it('should not return email template due to no receiver', async () => {
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);

      const result = await ImportedVoucherSubscriber.getEmailTemplate({
        companyId: 'companyId',
        voucherValue: 10,
        remainVoucher: 10,
      });

      expect(result).toBeFalsy();
    });
    it('should not return email template due to no base url', async () => {
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest.spyOn(systemConfigRepo, 'findOne').mockResolvedValueOnce(null);

      const result = await ImportedVoucherSubscriber.getEmailTemplate(prop);

      expect(result).toBeFalsy();
    });
  });
});
